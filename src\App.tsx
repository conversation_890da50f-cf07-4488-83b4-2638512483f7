import React, { useState } from 'react';
import { Shield, Upload, MessageSquare, Search, BarChart3 } from 'lucide-react';
import { motion } from 'framer-motion';
import FileUpload from './components/FileUpload';
import ChatInterface from './components/ChatInterface';
import Dashboard from './components/Dashboard';
import LogSearch from './components/LogSearch';

type TabType = 'dashboard' | 'upload' | 'chat' | 'search';

function App() {
  const [activeTab, setActiveTab] = useState<TabType>('dashboard');
  const [refreshKey, setRefreshKey] = useState(0);

  const handleUploadSuccess = () => {
    setRefreshKey(prev => prev + 1);
    setActiveTab('dashboard');
  };

  const tabs = [
    { id: 'dashboard', label: 'Dashboard', icon: BarChart3, color: 'text-blue-400' },
    { id: 'upload', label: 'Upload Logs', icon: Upload, color: 'text-green-400' },
    { id: 'chat', label: 'AI Assistant', icon: MessageSquare, color: 'text-cyan-400' },
    { id: 'search', label: 'Log Search', icon: Search, color: 'text-purple-400' }
  ];

  return (
    <div className="min-h-screen bg-gray-900">
      {/* Header */}
      <div className="bg-gray-800 border-b border-gray-700">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center gap-3">
              <motion.div
                className="p-2 bg-gradient-to-r from-blue-600 to-cyan-600 rounded-lg"
                whileHover={{ scale: 1.05 }}
              >
                <Shield className="w-8 h-8 text-white" />
              </motion.div>
              <div>
                <h1 className="text-xl font-bold text-white">FirewallAI</h1>
                <p className="text-sm text-gray-400">Intelligent Log Analysis Platform</p>
              </div>
            </div>
            <div className="flex items-center gap-2">
              <div className="w-2 h-2 bg-green-400 rounded-full"></div>
              <span className="text-sm text-gray-400">System Active</span>
            </div>
          </div>
        </div>
      </div>

      {/* Navigation */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
        <div className="flex space-x-1 bg-gray-800 p-1 rounded-lg mb-6">
          {tabs.map((tab) => (
            <motion.button
              key={tab.id}
              onClick={() => setActiveTab(tab.id as TabType)}
              className={`flex items-center gap-2 px-4 py-2 rounded-lg transition-all ${
                activeTab === tab.id
                  ? 'bg-gray-700 text-white shadow-lg'
                  : 'text-gray-400 hover:text-white hover:bg-gray-700/50'
              }`}
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
            >
              <tab.icon className={`w-4 h-4 ${activeTab === tab.id ? tab.color : ''}`} />
              <span className="font-medium">{tab.label}</span>
            </motion.button>
          ))}
        </div>

        {/* Content */}
        <motion.div
          key={activeTab}
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3 }}
        >
          {activeTab === 'dashboard' && <Dashboard key={refreshKey} />}
          {activeTab === 'upload' && <FileUpload onUploadSuccess={handleUploadSuccess} />}
          {activeTab === 'chat' && <ChatInterface />}
          {activeTab === 'search' && <LogSearch />}
        </motion.div>
      </div>
    </div>
  );
}

export default App;