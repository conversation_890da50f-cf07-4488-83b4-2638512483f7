/**
 * Extract IP address from log line
 * @param {string} logLine - The log line to parse
 * @returns {string|null} - Extracted IP address or null
 */
export function extractIP(logLine) {
  // IPv4 regex pattern
  const ipv4Regex = /\b(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\b/;
  
  // IPv6 regex pattern (simplified)
  const ipv6Regex = /\b(?:[0-9a-fA-F]{1,4}:){7}[0-9a-fA-F]{1,4}\b/;
  
  const ipv4Match = logLine.match(ipv4Regex);
  if (ipv4Match) {
    return ipv4Match[0];
  }
  
  const ipv6Match = logLine.match(ipv6Regex);
  if (ipv6Match) {
    return ipv6Match[0];
  }
  
  return null;
}

/**
 * Calculate threat score based on log content
 * @param {string} logLine - The log line to analyze
 * @returns {number} - Threat score (0-100)
 */
export function calculateThreatScore(logLine) {
  let score = 0;
  const line = logLine.toLowerCase();
  
  // High-risk indicators (20 points each)
  const highRiskPatterns = [
    'attack', 'exploit', 'malware', 'virus', 'trojan', 'backdoor',
    'injection', 'xss', 'csrf', 'ddos', 'botnet'
  ];
  
  // Medium-risk indicators (10 points each)
  const mediumRiskPatterns = [
    'failed', 'denied', 'blocked', 'rejected', 'unauthorized',
    'forbidden', 'suspicious', 'anomaly', 'intrusion'
  ];
  
  // Low-risk indicators (5 points each)
  const lowRiskPatterns = [
    'warning', 'error', 'timeout', 'retry', 'disconnect'
  ];
  
  // Port-based scoring
  const suspiciousPorts = [
    '22', '23', '135', '139', '445', '1433', '1521', '3389', '5432'
  ];
  
  // Protocol-based scoring
  if (line.includes('tcp') && line.includes('syn')) {
    score += 5; // Potential port scan
  }
  
  // Check high-risk patterns
  highRiskPatterns.forEach(pattern => {
    if (line.includes(pattern)) {
      score += 20;
    }
  });
  
  // Check medium-risk patterns
  mediumRiskPatterns.forEach(pattern => {
    if (line.includes(pattern)) {
      score += 10;
    }
  });
  
  // Check low-risk patterns
  lowRiskPatterns.forEach(pattern => {
    if (line.includes(pattern)) {
      score += 5;
    }
  });
  
  // Check for suspicious ports
  suspiciousPorts.forEach(port => {
    if (line.includes(port)) {
      score += 8;
    }
  });
  
  // Check for multiple failed attempts pattern
  if (line.match(/failed.*\d+.*times?/i)) {
    score += 15;
  }
  
  // Check for unusual time patterns (outside business hours)
  const timeMatch = line.match(/(\d{1,2}):(\d{2})/);
  if (timeMatch) {
    const hour = parseInt(timeMatch[1]);
    if (hour < 6 || hour > 22) {
      score += 3; // Activity outside normal hours
    }
  }
  
  // Check for rapid successive connections
  if (line.includes('rapid') || line.includes('burst') || line.includes('flood')) {
    score += 12;
  }
  
  // Geographic risk (simulated - in production, use IP geolocation)
  const ip = extractIP(logLine);
  if (ip) {
    // Simulate high-risk IP ranges
    if (ip.startsWith('10.0.0.') || ip.startsWith('192.168.')) {
      score -= 5; // Internal IPs are generally less risky
    } else {
      score += 2; // External IPs have slight risk increase
    }
  }
  
  // Ensure score is within bounds
  return Math.min(Math.max(score, 0), 100);
}

/**
 * Parse log timestamp
 * @param {string} logLine - The log line containing timestamp
 * @returns {Date|null} - Parsed date or null
 */
export function parseLogTimestamp(logLine) {
  // Common timestamp patterns
  const patterns = [
    // ISO format: 2024-01-15T10:30:15Z
    /(\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}(?:\.\d{3})?Z?)/,
    // Standard format: 2024-01-15 10:30:15
    /(\d{4}-\d{2}-\d{2}\s\d{2}:\d{2}:\d{2})/,
    // Syslog format: Jan 15 10:30:15
    /(Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec)\s+\d{1,2}\s+\d{2}:\d{2}:\d{2}/,
    // Unix timestamp
    /(\d{10})/
  ];
  
  for (const pattern of patterns) {
    const match = logLine.match(pattern);
    if (match) {
      const timestamp = new Date(match[1]);
      if (!isNaN(timestamp.getTime())) {
        return timestamp;
      }
    }
  }
  
  return null;
}

/**
 * Extract port numbers from log line
 * @param {string} logLine - The log line to parse
 * @returns {number[]} - Array of port numbers
 */
export function extractPorts(logLine) {
  const portRegex = /\b(\d{1,5})\b/g;
  const ports = [];
  let match;
  
  while ((match = portRegex.exec(logLine)) !== null) {
    const port = parseInt(match[1]);
    if (port >= 1 && port <= 65535) {
      ports.push(port);
    }
  }
  
  return [...new Set(ports)]; // Remove duplicates
}

/**
 * Determine log action (ALLOW, DENY, etc.)
 * @param {string} logLine - The log line to parse
 * @returns {string} - Action type
 */
export function extractAction(logLine) {
  const line = logLine.toUpperCase();
  
  if (line.includes('ALLOW') || line.includes('ACCEPT') || line.includes('PERMIT')) {
    return 'ALLOW';
  }
  if (line.includes('DENY') || line.includes('DROP') || line.includes('REJECT') || line.includes('BLOCK')) {
    return 'DENY';
  }
  if (line.includes('LOG') || line.includes('AUDIT')) {
    return 'LOG';
  }
  
  return 'UNKNOWN';
}

/**
 * Extract protocol from log line
 * @param {string} logLine - The log line to parse
 * @returns {string} - Protocol (TCP, UDP, ICMP, etc.)
 */
export function extractProtocol(logLine) {
  const line = logLine.toUpperCase();
  
  if (line.includes('TCP')) return 'TCP';
  if (line.includes('UDP')) return 'UDP';
  if (line.includes('ICMP')) return 'ICMP';
  if (line.includes('HTTP')) return 'HTTP';
  if (line.includes('HTTPS')) return 'HTTPS';
  if (line.includes('SSH')) return 'SSH';
  if (line.includes('FTP')) return 'FTP';
  if (line.includes('SMTP')) return 'SMTP';
  
  return 'UNKNOWN';
}

/**
 * Validate log line format
 * @param {string} logLine - The log line to validate
 * @returns {boolean} - True if valid format
 */
export function isValidLogLine(logLine) {
  if (!logLine || typeof logLine !== 'string' || logLine.trim().length === 0) {
    return false;
  }
  
  // Check for minimum required elements (timestamp, action, or IP)
  const hasTimestamp = parseLogTimestamp(logLine) !== null;
  const hasIP = extractIP(logLine) !== null;
  const hasAction = extractAction(logLine) !== 'UNKNOWN';
  
  return hasTimestamp || hasIP || hasAction;
}

/**
 * Sanitize log line for safe storage/display
 * @param {string} logLine - The log line to sanitize
 * @returns {string} - Sanitized log line
 */
export function sanitizeLogLine(logLine) {
  if (!logLine || typeof logLine !== 'string') {
    return '';
  }
  
  // Remove potentially dangerous characters
  return logLine
    .replace(/[<>]/g, '') // Remove HTML tags
    .replace(/[\x00-\x1F\x7F]/g, '') // Remove control characters
    .trim()
    .substring(0, 2000); // Limit length
}
