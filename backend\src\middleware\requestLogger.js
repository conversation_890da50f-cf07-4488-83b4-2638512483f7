const requestLogger = (req, res, next) => {
  const start = Date.now();
  const timestamp = new Date().toISOString();
  
  // Log request
  console.log(`[${timestamp}] ${req.method} ${req.url} - ${req.ip}`);
  
  // Log request body for POST/PUT requests (excluding file uploads)
  if ((req.method === 'POST' || req.method === 'PUT') && 
      !req.url.includes('/upload') && 
      req.body && 
      Object.keys(req.body).length > 0) {
    console.log('Request body:', JSON.stringify(req.body, null, 2));
  }

  // Override res.json to log response
  const originalJson = res.json;
  res.json = function(data) {
    const duration = Date.now() - start;
    console.log(`[${new Date().toISOString()}] ${req.method} ${req.url} - ${res.statusCode} - ${duration}ms`);
    
    // Log response for errors or in development
    if (res.statusCode >= 400 || process.env.NODE_ENV === 'development') {
      console.log('Response:', JSON.stringify(data, null, 2));
    }
    
    return originalJson.call(this, data);
  };

  next();
};

export default requestLogger;
