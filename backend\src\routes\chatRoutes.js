import express from 'express';
import chatController from '../controllers/chatController.js';
import validateRequest from '../middleware/validateRequest.js';

const router = express.Router();

// Send chat query
router.post('/query', 
  validateRequest.chatMessage,
  chatController.sendQuery
);

// Get conversation history
router.get('/history', 
  chatController.getConversationHistory
);

// Clear conversation history
router.delete('/history', 
  chatController.clearConversations
);

// Get suggested queries
router.get('/suggestions', 
  chatController.getSuggestedQueries
);

export default router;
