import React, { useState, useEffect } from 'react';
import { Search, Filter, Calendar, AlertTriangle, Clock } from 'lucide-react';
import { motion } from 'framer-motion';
import { api } from '../services/api';
import { LogEntry, SearchFilters } from '../types';

const LogSearch: React.FC = () => {
  const [logs, setLogs] = useState<LogEntry[]>([]);
  const [filters, setFilters] = useState<SearchFilters>({
    query: '',
    startDate: '',
    endDate: '',
    minThreat: 0
  });
  const [isLoading, setIsLoading] = useState(false);
  const [total, setTotal] = useState(0);

  const searchLogs = async () => {
    setIsLoading(true);
    try {
      const response = await api.searchLogs(filters);
      setLogs(response.logs);
      setTotal(response.total);
    } catch (error) {
      console.error('Search failed:', error);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    searchLogs();
  }, []);

  const handleFilterChange = (key: keyof SearchFilters, value: string | number) => {
    setFilters(prev => ({ ...prev, [key]: value }));
  };

  const getThreatColor = (score: number) => {
    if (score >= 70) return 'text-red-400';
    if (score >= 30) return 'text-yellow-400';
    return 'text-green-400';
  };

  const getThreatBadgeColor = (score: number) => {
    if (score >= 70) return 'bg-red-900/20 border-red-500/20 text-red-400';
    if (score >= 30) return 'bg-yellow-900/20 border-yellow-500/20 text-yellow-400';
    return 'bg-green-900/20 border-green-500/20 text-green-400';
  };

  return (
    <div className="bg-gray-800 rounded-xl p-6 border border-gray-700">
      <div className="flex items-center gap-2 mb-6">
        <Search className="w-5 h-5 text-cyan-400" />
        <h2 className="text-xl font-semibold text-white">Log Search & Analysis</h2>
      </div>

      {/* Search Filters */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
        <div>
          <label className="block text-sm font-medium text-gray-300 mb-2">
            Search Query
          </label>
          <input
            type="text"
            value={filters.query}
            onChange={(e) => handleFilterChange('query', e.target.value)}
            placeholder="IP, keyword, or pattern..."
            className="w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-cyan-500"
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-300 mb-2">
            Start Date
          </label>
          <input
            type="datetime-local"
            value={filters.startDate}
            onChange={(e) => handleFilterChange('startDate', e.target.value)}
            className="w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:ring-2 focus:ring-cyan-500"
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-300 mb-2">
            End Date
          </label>
          <input
            type="datetime-local"
            value={filters.endDate}
            onChange={(e) => handleFilterChange('endDate', e.target.value)}
            className="w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:ring-2 focus:ring-cyan-500"
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-300 mb-2">
            Min Threat Score
          </label>
          <select
            value={filters.minThreat}
            onChange={(e) => handleFilterChange('minThreat', parseInt(e.target.value))}
            className="w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:ring-2 focus:ring-cyan-500"
          >
            <option value={0}>All Levels</option>
            <option value={30}>Medium+</option>
            <option value={70}>High Only</option>
          </select>
        </div>
      </div>

      {/* Search Button */}
      <motion.button
        onClick={searchLogs}
        disabled={isLoading}
        className="mb-6 px-6 py-2 bg-cyan-600 hover:bg-cyan-700 disabled:bg-gray-600 text-white rounded-lg transition-colors flex items-center gap-2"
        whileHover={{ scale: 1.05 }}
        whileTap={{ scale: 0.95 }}
      >
        <Filter className="w-4 h-4" />
        {isLoading ? 'Searching...' : 'Search Logs'}
      </motion.button>

      {/* Results Summary */}
      {!isLoading && (
        <div className="mb-4 flex items-center gap-2 text-sm text-gray-400">
          <span>Found {total} matching logs</span>
          {logs.length < total && <span>(showing first {logs.length})</span>}
        </div>
      )}

      {/* Results */}
      <div className="space-y-2 max-h-96 overflow-y-auto">
        {isLoading ? (
          <div className="flex items-center justify-center py-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-cyan-400"></div>
          </div>
        ) : logs.length === 0 ? (
          <div className="text-center py-8 text-gray-400">
            No logs found matching your criteria
          </div>
        ) : (
          logs.map((log, index) => (
            <motion.div
              key={log.id}
              className="p-3 bg-gray-700 rounded-lg border border-gray-600"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: index * 0.05 }}
            >
              <div className="flex items-start justify-between gap-4">
                <div className="flex-1">
                  <div className="flex items-center gap-2 mb-1">
                    <Clock className="w-4 h-4 text-gray-400" />
                    <span className="text-sm text-gray-300">
                      {new Date(log.timestamp).toLocaleString()}
                    </span>
                    {log.source_ip && (
                      <>
                        <span className="text-gray-500">•</span>
                        <span className="text-sm text-blue-400">{log.source_ip}</span>
                      </>
                    )}
                  </div>
                  <p className="text-sm text-gray-100 font-mono bg-gray-800 p-2 rounded">
                    {log.log_line}
                  </p>
                </div>
                <div className="flex items-center gap-2">
                  {log.threat_score > 0 && (
                    <div className={`px-2 py-1 rounded-full text-xs border ${getThreatBadgeColor(log.threat_score)}`}>
                      <AlertTriangle className="w-3 h-3 inline mr-1" />
                      {log.threat_score}
                    </div>
                  )}
                </div>
              </div>
            </motion.div>
          ))
        )}
      </div>
    </div>
  );
};

export default LogSearch;