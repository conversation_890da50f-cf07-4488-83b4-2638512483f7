export interface LogEntry {
  id: number;
  log_line: string;
  timestamp: string;
  source_ip: string | null;
  threat_score: number;
  metadata_json: string;
}

export interface ChatMessage {
  id: string;
  message: string;
  response: string;
  timestamp: string;
  isUser: boolean;
}

export interface DashboardInsights {
  totalLogs: number;
  threatsDetected: number;
  topIPs: Array<{ ip: string; count: number }>;
  recentActivity: Array<{
    timestamp: string;
    message: string;
    threat_score: number;
    source_ip: string;
  }>;
}

export interface UploadResponse {
  success: boolean;
  message: string;
  logsProcessed: number;
}

export interface SearchFilters {
  query: string;
  startDate: string;
  endDate: string;
  minThreat: number;
}