// Mock data storage for demonstration
// In production, this would connect to a real database

class LogModel {
  constructor() {
    this.logs = [];
    this.initializeSampleData();
  }

  initializeSampleData() {
    const sampleLogs = [
      {
        id: 1,
        log_line: "2024-01-15 10:30:15 DENY TCP ************* 80 443 - - - RECEIVE",
        timestamp: new Date().toISOString(),
        source_ip: "*************",
        threat_score: 25,
        metadata_json: JSON.stringify({ sample: true })
      },
      {
        id: 2,
        log_line: "2024-01-15 10:31:22 DENY TCP ********* 22 22 - - - RECEIVE",
        timestamp: new Date().toISOString(),
        source_ip: "*********",
        threat_score: 45,
        metadata_json: JSON.stringify({ sample: true })
      },
      {
        id: 3,
        log_line: "2024-01-15 10:32:10 ALLOW TCP ************* 443 443 - - - SEND",
        timestamp: new Date().toISOString(),
        source_ip: "*************",
        threat_score: 0,
        metadata_json: JSON.stringify({ sample: true })
      }
    ];
    
    this.logs = sampleLogs;
  }

  async getAllLogs() {
    return this.logs;
  }

  async getLogById(id) {
    return this.logs.find(log => log.id === parseInt(id));
  }

  async createLog(logData) {
    const newLog = {
      id: Date.now(),
      ...logData,
      timestamp: new Date().toISOString()
    };
    this.logs.push(newLog);
    return newLog;
  }

  async createMultipleLogs(logsData) {
    const newLogs = logsData.map((logData, index) => ({
      id: Date.now() + index,
      ...logData,
      timestamp: new Date().toISOString()
    }));
    this.logs.push(...newLogs);
    return newLogs;
  }

  async searchLogs(filters = {}) {
    let filteredLogs = [...this.logs];
    
    if (filters.query) {
      filteredLogs = filteredLogs.filter(log => 
        log.log_line.toLowerCase().includes(filters.query.toLowerCase()) ||
        log.source_ip?.includes(filters.query)
      );
    }
    
    if (filters.startDate && filters.endDate) {
      filteredLogs = filteredLogs.filter(log => {
        const logDate = new Date(log.timestamp);
        return logDate >= new Date(filters.startDate) && logDate <= new Date(filters.endDate);
      });
    }
    
    if (filters.minThreat) {
      filteredLogs = filteredLogs.filter(log => log.threat_score >= parseInt(filters.minThreat));
    }
    
    return {
      logs: filteredLogs.slice(0, 100), // Limit results
      total: filteredLogs.length
    };
  }

  async getLogStats() {
    const totalLogs = this.logs.length;
    const threatsDetected = this.logs.filter(log => log.threat_score > 0).length;
    
    // Top IPs
    const ipCounts = {};
    this.logs.forEach(log => {
      if (log.source_ip) {
        ipCounts[log.source_ip] = (ipCounts[log.source_ip] || 0) + 1;
      }
    });
    
    const topIPs = Object.entries(ipCounts)
      .sort(([,a], [,b]) => b - a)
      .slice(0, 10)
      .map(([ip, count]) => ({ ip, count }));
    
    // Recent activity
    const recentActivity = this.logs
      .filter(log => log.threat_score > 0)
      .sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp))
      .slice(0, 5)
      .map(log => ({
        timestamp: log.timestamp,
        message: log.log_line.substring(0, 100) + '...',
        threat_score: log.threat_score,
        source_ip: log.source_ip
      }));

    return {
      totalLogs,
      threatsDetected,
      topIPs,
      recentActivity
    };
  }
}

export default new LogModel();
