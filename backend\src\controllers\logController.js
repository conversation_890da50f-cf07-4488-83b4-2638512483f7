import LogModel from '../models/LogModel.js';
import { extractIP, calculateThreatScore } from '../utils/logUtils.js';
import fs from 'fs';

class LogController {
  async uploadLogs(req, res) {
    try {
      if (!req.file) {
        return res.status(400).json({ error: 'No file uploaded' });
      }

      const filePath = req.file.path;
      const fileContent = fs.readFileSync(filePath, 'utf8');
      const lines = fileContent.split('\n').filter(line => line.trim());

      // Process logs
      const processedLogs = lines.map((line, index) => {
        const sourceIP = extractIP(line);
        const threatScore = calculateThreatScore(line);
        
        return {
          log_line: line,
          source_ip: sourceIP,
          threat_score: threatScore,
          metadata_json: JSON.stringify({
            file_name: req.file.originalname,
            line_number: index + 1,
            file_size: req.file.size,
            upload_time: new Date().toISOString()
          })
        };
      });

      // Save logs to model
      const savedLogs = await LogModel.createMultipleLogs(processedLogs);

      // Clean up uploaded file
      fs.unlinkSync(filePath);

      // Simulate AI processing delay
      setTimeout(() => {
        console.log(`AI processing completed for ${savedLogs.length} logs`);
      }, 2000);

      res.json({
        success: true,
        message: `Successfully processed ${lines.length} log entries`,
        logsProcessed: lines.length,
        threatsDetected: processedLogs.filter(log => log.threat_score > 0).length
      });

    } catch (error) {
      console.error('Upload error:', error);
      
      // Clean up file if it exists
      if (req.file && fs.existsSync(req.file.path)) {
        fs.unlinkSync(req.file.path);
      }
      
      res.status(500).json({ 
        error: 'Failed to process log file',
        details: error.message 
      });
    }
  }

  async searchLogs(req, res) {
    try {
      const filters = {
        query: req.query.query || '',
        startDate: req.query.startDate || '',
        endDate: req.query.endDate || '',
        minThreat: req.query.minThreat || 0
      };

      const result = await LogModel.searchLogs(filters);
      
      res.json(result);
    } catch (error) {
      console.error('Log search error:', error);
      res.status(500).json({ 
        error: 'Failed to search logs',
        details: error.message 
      });
    }
  }

  async getAllLogs(req, res) {
    try {
      const page = parseInt(req.query.page) || 1;
      const limit = parseInt(req.query.limit) || 50;
      const offset = (page - 1) * limit;

      const allLogs = await LogModel.getAllLogs();
      const paginatedLogs = allLogs.slice(offset, offset + limit);

      res.json({
        logs: paginatedLogs,
        total: allLogs.length,
        page,
        limit,
        totalPages: Math.ceil(allLogs.length / limit)
      });
    } catch (error) {
      console.error('Get logs error:', error);
      res.status(500).json({ 
        error: 'Failed to retrieve logs',
        details: error.message 
      });
    }
  }

  async getLogById(req, res) {
    try {
      const { id } = req.params;
      const log = await LogModel.getLogById(id);

      if (!log) {
        return res.status(404).json({ error: 'Log not found' });
      }

      res.json(log);
    } catch (error) {
      console.error('Get log by ID error:', error);
      res.status(500).json({ 
        error: 'Failed to retrieve log',
        details: error.message 
      });
    }
  }

  async getLogStatistics(req, res) {
    try {
      const stats = await LogModel.getLogStats();
      res.json(stats);
    } catch (error) {
      console.error('Get log statistics error:', error);
      res.status(500).json({ 
        error: 'Failed to retrieve log statistics',
        details: error.message 
      });
    }
  }

  async deleteLogs(req, res) {
    try {
      // In a real implementation, this would delete logs from the database
      // For now, we'll just reset the model
      LogModel.logs = [];
      LogModel.initializeSampleData();

      res.json({
        success: true,
        message: 'All logs have been cleared and sample data restored'
      });
    } catch (error) {
      console.error('Delete logs error:', error);
      res.status(500).json({ 
        error: 'Failed to delete logs',
        details: error.message 
      });
    }
  }
}

export default new LogController();
