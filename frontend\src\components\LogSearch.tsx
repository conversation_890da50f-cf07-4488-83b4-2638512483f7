import React, { useState, useEffect } from 'react';
import { Search, Filter, Calendar, AlertTriangle, Clock, RefreshCw, Download, Eye } from 'lucide-react';
import { motion } from 'framer-motion';
import { api, handleApiError } from '../services/api';
import { LogEntry, SearchFilters } from '../types';

const LogSearch: React.FC = () => {
  const [logs, setLogs] = useState<LogEntry[]>([]);
  const [filters, setFilters] = useState<SearchFilters>({
    query: '',
    startDate: '',
    endDate: '',
    minThreat: 0
  });
  const [isLoading, setIsLoading] = useState(false);
  const [total, setTotal] = useState(0);
  const [error, setError] = useState<string | null>(null);
  const [selectedLog, setSelectedLog] = useState<LogEntry | null>(null);

  const searchLogs = async () => {
    setIsLoading(true);
    setError(null);
    try {
      const response = await api.searchLogs(filters);
      setLogs(response.logs);
      setTotal(response.total);
    } catch (error: any) {
      const errorMessage = handleApiError(error);
      setError(errorMessage);
      console.error('Search failed:', error);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    searchLogs();
  }, []);

  const handleFilterChange = (key: keyof SearchFilters, value: string | number) => {
    setFilters(prev => ({ ...prev, [key]: value }));
  };

  const handleQuickFilter = (filterType: string) => {
    switch (filterType) {
      case 'high-threats':
        setFilters(prev => ({ ...prev, minThreat: 70 }));
        break;
      case 'medium-threats':
        setFilters(prev => ({ ...prev, minThreat: 30 }));
        break;
      case 'today':
        const today = new Date();
        const startOfDay = new Date(today.getFullYear(), today.getMonth(), today.getDate());
        setFilters(prev => ({
          ...prev,
          startDate: startOfDay.toISOString().slice(0, 16),
          endDate: new Date().toISOString().slice(0, 16)
        }));
        break;
      case 'clear':
        setFilters({
          query: '',
          startDate: '',
          endDate: '',
          minThreat: 0
        });
        break;
    }
  };

  const getThreatColor = (score: number) => {
    if (score >= 70) return 'text-red-400';
    if (score >= 30) return 'text-yellow-400';
    return 'text-green-400';
  };

  const getThreatBadgeColor = (score: number) => {
    if (score >= 70) return 'bg-red-900/20 border-red-500/20 text-red-400';
    if (score >= 30) return 'bg-yellow-900/20 border-yellow-500/20 text-yellow-400';
    return 'bg-green-900/20 border-green-500/20 text-green-400';
  };

  const exportLogs = () => {
    const csvContent = [
      ['ID', 'Timestamp', 'Source IP', 'Threat Score', 'Log Line'],
      ...logs.map(log => [
        log.id,
        log.timestamp,
        log.source_ip || 'N/A',
        log.threat_score,
        log.log_line.replace(/,/g, ';') // Replace commas to avoid CSV issues
      ])
    ].map(row => row.join(',')).join('\n');

    const blob = new Blob([csvContent], { type: 'text/csv' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `firewall-logs-${new Date().toISOString().split('T')[0]}.csv`;
    a.click();
    window.URL.revokeObjectURL(url);
  };

  return (
    <div className="space-y-6">
      <div className="bg-gray-800 rounded-xl p-6 border border-gray-700">
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center gap-2">
            <Search className="w-5 h-5 text-cyan-400" />
            <h2 className="text-xl font-semibold text-white">Log Search & Analysis</h2>
          </div>
          <div className="flex items-center gap-2">
            <button
              onClick={exportLogs}
              disabled={logs.length === 0}
              className="px-3 py-2 bg-green-600 hover:bg-green-700 disabled:bg-gray-600 text-white rounded-lg transition-colors flex items-center gap-2 text-sm"
              title="Export to CSV"
            >
              <Download className="w-4 h-4" />
              Export
            </button>
          </div>
        </div>

        {/* Quick Filters */}
        <div className="flex flex-wrap gap-2 mb-4">
          <button
            onClick={() => handleQuickFilter('high-threats')}
            className="px-3 py-1 bg-red-600/20 hover:bg-red-600/30 text-red-400 rounded-full text-sm transition-colors"
          >
            High Threats
          </button>
          <button
            onClick={() => handleQuickFilter('medium-threats')}
            className="px-3 py-1 bg-yellow-600/20 hover:bg-yellow-600/30 text-yellow-400 rounded-full text-sm transition-colors"
          >
            Medium+ Threats
          </button>
          <button
            onClick={() => handleQuickFilter('today')}
            className="px-3 py-1 bg-blue-600/20 hover:bg-blue-600/30 text-blue-400 rounded-full text-sm transition-colors"
          >
            Today
          </button>
          <button
            onClick={() => handleQuickFilter('clear')}
            className="px-3 py-1 bg-gray-600/20 hover:bg-gray-600/30 text-gray-400 rounded-full text-sm transition-colors"
          >
            Clear Filters
          </button>
        </div>

        {/* Search Filters */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
          <div>
            <label className="block text-sm font-medium text-gray-300 mb-2">
              Search Query
            </label>
            <input
              type="text"
              value={filters.query}
              onChange={(e) => handleFilterChange('query', e.target.value)}
              placeholder="IP, keyword, or pattern..."
              className="w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-cyan-500"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-300 mb-2">
              Start Date
            </label>
            <input
              type="datetime-local"
              value={filters.startDate}
              onChange={(e) => handleFilterChange('startDate', e.target.value)}
              className="w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:ring-2 focus:ring-cyan-500"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-300 mb-2">
              End Date
            </label>
            <input
              type="datetime-local"
              value={filters.endDate}
              onChange={(e) => handleFilterChange('endDate', e.target.value)}
              className="w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:ring-2 focus:ring-cyan-500"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-300 mb-2">
              Min Threat Score
            </label>
            <select
              value={filters.minThreat}
              onChange={(e) => handleFilterChange('minThreat', parseInt(e.target.value))}
              className="w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:ring-2 focus:ring-cyan-500"
            >
              <option value={0}>All Levels</option>
              <option value={30}>Medium+ (30+)</option>
              <option value={50}>High+ (50+)</option>
              <option value={70}>Critical (70+)</option>
            </select>
          </div>
        </div>

        {/* Search Button */}
        <motion.button
          onClick={searchLogs}
          disabled={isLoading}
          className="mb-6 px-6 py-2 bg-cyan-600 hover:bg-cyan-700 disabled:bg-gray-600 text-white rounded-lg transition-colors flex items-center gap-2"
          whileHover={{ scale: 1.05 }}
          whileTap={{ scale: 0.95 }}
        >
          {isLoading ? (
            <RefreshCw className="w-4 h-4 animate-spin" />
          ) : (
            <Filter className="w-4 h-4" />
          )}
          {isLoading ? 'Searching...' : 'Search Logs'}
        </motion.button>

        {/* Error Display */}
        {error && (
          <motion.div
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            className="mb-4 p-3 bg-red-900/20 border border-red-500/20 rounded-lg"
          >
            <p className="text-red-400 text-sm">{error}</p>
          </motion.div>
        )}

        {/* Results Summary */}
        {!isLoading && !error && (
          <div className="mb-4 flex items-center justify-between text-sm text-gray-400">
            <span>Found {total.toLocaleString()} matching logs</span>
            {logs.length < total && <span>(showing first {logs.length})</span>}
          </div>
        )}

        {/* Results */}
        <div className="space-y-2 max-h-96 overflow-y-auto">
          {isLoading ? (
            <div className="flex items-center justify-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-cyan-400"></div>
              <span className="ml-3 text-gray-400">Searching logs...</span>
            </div>
          ) : logs.length === 0 ? (
            <div className="text-center py-8 text-gray-400">
              <Search className="w-12 h-12 mx-auto mb-3 opacity-50" />
              <p>No logs found matching your criteria</p>
              <p className="text-sm mt-1">Try adjusting your search filters</p>
            </div>
          ) : (
            logs.map((log, index) => (
              <motion.div
                key={log.id}
                className="p-3 bg-gray-700 rounded-lg border border-gray-600 hover:border-gray-500 transition-colors cursor-pointer"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: index * 0.05 }}
                onClick={() => setSelectedLog(log)}
              >
                <div className="flex items-start justify-between gap-4">
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center gap-2 mb-1">
                      <Clock className="w-4 h-4 text-gray-400 flex-shrink-0" />
                      <span className="text-sm text-gray-300">
                        {new Date(log.timestamp).toLocaleString()}
                      </span>
                      {log.source_ip && (
                        <>
                          <span className="text-gray-500">•</span>
                          <span className="text-sm text-blue-400">{log.source_ip}</span>
                        </>
                      )}
                      <button
                        onClick={(e) => {
                          e.stopPropagation();
                          setSelectedLog(log);
                        }}
                        className="ml-auto p-1 hover:bg-gray-600 rounded"
                        title="View details"
                      >
                        <Eye className="w-3 h-3 text-gray-400" />
                      </button>
                    </div>
                    <p className="text-sm text-gray-100 font-mono bg-gray-800 p-2 rounded truncate">
                      {log.log_line}
                    </p>
                  </div>
                  <div className="flex items-center gap-2 flex-shrink-0">
                    {log.threat_score > 0 && (
                      <div className={`px-2 py-1 rounded-full text-xs border ${getThreatBadgeColor(log.threat_score)}`}>
                        <AlertTriangle className="w-3 h-3 inline mr-1" />
                        {log.threat_score}
                      </div>
                    )}
                  </div>
                </div>
              </motion.div>
            ))
          )}
        </div>
      </div>

      {/* Log Detail Modal */}
      {selectedLog && (
        <motion.div
          className="fixed inset-0 bg-black/50 flex items-center justify-center p-4 z-50"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          onClick={() => setSelectedLog(null)}
        >
          <motion.div
            className="bg-gray-800 rounded-xl p-6 max-w-4xl w-full max-h-[80vh] overflow-y-auto border border-gray-700"
            initial={{ scale: 0.9, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            onClick={(e) => e.stopPropagation()}
          >
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-semibold text-white">Log Details</h3>
              <button
                onClick={() => setSelectedLog(null)}
                className="text-gray-400 hover:text-white"
              >
                ✕
              </button>
            </div>
            
            <div className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-1">ID</label>
                  <p className="text-white">{selectedLog.id}</p>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-1">Timestamp</label>
                  <p className="text-white">{new Date(selectedLog.timestamp).toLocaleString()}</p>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-1">Source IP</label>
                  <p className="text-blue-400">{selectedLog.source_ip || 'N/A'}</p>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-1">Threat Score</label>
                  <p className={getThreatColor(selectedLog.threat_score)}>
                    {selectedLog.threat_score}
                  </p>
                </div>
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-1">Log Line</label>
                <pre className="text-sm text-gray-100 bg-gray-900 p-3 rounded overflow-x-auto">
                  {selectedLog.log_line}
                </pre>
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-1">Metadata</label>
                <pre className="text-sm text-gray-300 bg-gray-900 p-3 rounded overflow-x-auto">
                  {JSON.stringify(JSON.parse(selectedLog.metadata_json), null, 2)}
                </pre>
              </div>
            </div>
          </motion.div>
        </motion.div>
      )}
    </div>
  );
};

export default LogSearch;
