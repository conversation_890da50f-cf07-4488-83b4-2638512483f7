import LogModel from './LogModel.js';

class ChatModel {
  constructor() {
    this.conversations = [];
  }

  async generateResponse(message, context = '') {
    // Get current log statistics for context
    const stats = await LogModel.getLogStats();
    
    // Simulate AI response based on message content
    const responses = {
      'threats': this.generateThreatAnalysis(stats),
      'summary': this.generateSummaryResponse(stats),
      'patterns': this.generatePatternAnalysis(stats),
      'recommendations': this.generateRecommendations(stats),
      'default': this.generateDefaultResponse(stats)
    };
    
    // Find the best matching response
    const key = Object.keys(responses).find(k => 
      message.toLowerCase().includes(k)
    ) || 'default';
    
    const response = {
      response: responses[key],
      timestamp: new Date().toISOString(),
      context: {
        totalLogs: stats.totalLogs,
        threatsDetected: stats.threatsDetected,
        topIPs: stats.topIPs.slice(0, 3)
      }
    };

    // Store conversation
    this.conversations.push({
      id: Date.now(),
      message,
      response: response.response,
      timestamp: response.timestamp
    });

    return response;
  }

  generateThreatAnalysis(stats) {
    return `🔥 **Threat Analysis**

Based on the current logs, I've identified:
- **${stats.threatsDetected}** potential threats out of ${stats.totalLogs} total logs
- **Top threat sources:** ${stats.topIPs.slice(0, 3).map(ip => ip.ip).join(', ')}
- **Threat rate:** ${((stats.threatsDetected / stats.totalLogs) * 100).toFixed(1)}%

🛡 **Recommendations:**
- Monitor IP ${stats.topIPs[0]?.ip} for unusual activity (${stats.topIPs[0]?.count} requests)
- Consider implementing rate limiting for high-frequency IPs
- Review firewall rules for commonly targeted ports (22, 80, 443)
- Set up automated alerts for threat scores above 70`;
  }

  generateSummaryResponse(stats) {
    return `📊 **Log Summary**

**Current Status:**
- Total logs processed: **${stats.totalLogs}**
- Threats detected: **${stats.threatsDetected}**
- Most active IPs: ${stats.topIPs.slice(0, 3).map(ip => `${ip.ip} (${ip.count})`).join(', ')}

⏱ **Timeline:** Last 24 hours
🧠 **Analysis:** ${stats.threatsDetected > 0 ? 'High activity from suspicious IPs detected' : 'Normal traffic patterns observed'}

**Recent Activity:**
${stats.recentActivity.slice(0, 3).map(activity => 
  `• ${activity.source_ip} - Threat Score: ${activity.threat_score}`
).join('\n')}`;
  }

  generatePatternAnalysis(stats) {
    return `🔍 **Pattern Analysis**

**Traffic Patterns Detected:**
- **${stats.topIPs.length}** unique IP addresses observed
- **${stats.threatsDetected}** suspicious activities identified
- **Peak activity** from: ${stats.topIPs[0]?.ip} (${stats.topIPs[0]?.count} requests)

**Behavioral Insights:**
- Multiple connection attempts suggest potential scanning activity
- Port-specific targeting indicates focused attack vectors
- Time-based patterns show automated vs. manual activities

**Risk Assessment:**
${stats.threatsDetected > 10 ? '🔴 High Risk - Multiple threats detected' : 
  stats.threatsDetected > 5 ? '🟡 Medium Risk - Some threats present' : 
  '🟢 Low Risk - Minimal threats detected'}`;
  }

  generateRecommendations(stats) {
    return `💡 **Security Recommendations**

**Immediate Actions:**
1. **Block suspicious IPs:** ${stats.topIPs.filter(ip => ip.count > 5).map(ip => ip.ip).join(', ')}
2. **Implement rate limiting** for IPs with >10 requests/hour
3. **Review firewall rules** for commonly attacked ports

**Long-term Strategies:**
- Set up **automated threat detection** with real-time alerts
- Implement **IP reputation checking** for incoming connections
- Configure **geographic blocking** for high-risk regions
- Enable **deep packet inspection** for advanced threat detection

**Monitoring Enhancements:**
- Create dashboards for real-time threat visualization
- Set up email/SMS alerts for critical threats (score >80)
- Implement log correlation with external threat intelligence`;
  }

  generateDefaultResponse(stats) {
    return `🤖 **AI Log Analysis Assistant**

I've analyzed your firewall logs and here's what I found:

**🔍 Key Findings:**
- **${stats.totalLogs}** total log entries processed
- **${stats.threatsDetected}** potential security threats identified
- **${stats.topIPs.length}** unique IP addresses detected

**📈 Trend Analysis:**
${stats.threatsDetected > 0 ? 
  'Suspicious activity patterns detected. Consider implementing additional security measures.' :
  'Traffic patterns appear normal with minimal security concerns.'}

**💬 How I can help:**
- Ask about "threats" for detailed threat analysis
- Ask for a "summary" for current status overview
- Ask about "patterns" for behavioral insights
- Ask for "recommendations" for security improvements

What would you like to know more about?`;
  }

  async getConversationHistory(limit = 10) {
    return this.conversations
      .sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp))
      .slice(0, limit);
  }

  async clearConversations() {
    this.conversations = [];
    return { success: true, message: 'Conversation history cleared' };
  }
}

export default new ChatModel();
