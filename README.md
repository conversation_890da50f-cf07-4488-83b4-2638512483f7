# FirewallAI - Intelligent Log Analysis Platform

A modern, AI-powered firewall log analysis platform built with React and Node.js. This application provides real-time threat detection, intelligent log parsing, and interactive security insights.

## 🚀 Features

- **Real-time Log Analysis**: Upload and analyze firewall logs instantly
- **AI-Powered Insights**: Get intelligent threat analysis and security recommendations
- **Interactive Dashboard**: Visualize security metrics with charts and graphs
- **Advanced Search**: Filter and search logs with multiple criteria
- **Threat Detection**: Automatic threat scoring and classification
- **Export Capabilities**: Export filtered logs to CSV format
- **Responsive Design**: Modern, dark-themed UI that works on all devices

## 🏗️ Architecture

The project is organized into two main parts:

```
firewall-log-analyzer/
├── backend/                 # Node.js API server
│   ├── src/
│   │   ├── controllers/     # Request handlers
│   │   ├── models/          # Data models
│   │   ├── routes/          # API routes
│   │   ├── middleware/      # Custom middleware
│   │   ├── utils/           # Utility functions
│   │   └── server.js        # Main server file
│   ├── uploads/             # File upload directory
│   └── package.json
├── frontend/                # React application
│   ├── src/
│   │   ├── components/      # React components
│   │   ├── hooks/           # Custom React hooks
│   │   ├── services/        # API services
│   │   ├── types/           # TypeScript types
│   │   └── App.tsx          # Main app component
│   └── package.json
└── package.json             # Root package.json
```

## 🛠️ Technology Stack

### Backend
- **Node.js** - Runtime environment
- **Express.js** - Web framework
- **Multer** - File upload handling
- **CORS** - Cross-origin resource sharing
- **dotenv** - Environment configuration

### Frontend
- **React 18** - UI framework
- **TypeScript** - Type safety
- **Vite** - Build tool and dev server
- **Tailwind CSS** - Styling framework
- **Framer Motion** - Animations
- **Recharts** - Data visualization
- **Lucide React** - Icons
- **React Dropzone** - File upload UI
- **Axios** - HTTP client

## 📦 Installation

### Prerequisites
- Node.js (v18 or higher)
- npm or yarn

### Quick Start

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd firewall-log-analyzer
   ```

2. **Install dependencies**
   ```bash
   npm run install:all
   ```

3. **Configure environment variables**
   ```bash
   # Copy the example environment file in the backend directory
   cp backend/.env.example backend/.env
   
   # Edit the .env file with your configuration
   ```

4. **Start the development servers**
   ```bash
   npm run dev
   ```

   This will start both the backend (port 3003) and frontend (port 5173) servers.

### Manual Installation

If you prefer to install and run each part separately:

**Backend:**
```bash
cd backend
npm install
npm run dev
```

**Frontend:**
```bash
cd frontend
npm install
npm run dev
```

## 🔧 Configuration

### Backend Configuration (backend/.env)

```env
# Server Configuration
PORT=3003
NODE_ENV=development

# Database Configuration (if using a database)
DB_HOST=localhost
DB_USER=root
DB_PASSWORD=
DB_NAME=firewall_logs

# AI Configuration
CLAUDE_API_KEY=your_claude_api_key_here

# Security
JWT_SECRET=your_jwt_secret_here

# CORS
FRONTEND_URL=http://localhost:5173
```

### Frontend Configuration

The frontend automatically proxies API requests to the backend. The proxy configuration is in `frontend/vite.config.ts`.

## 🚀 Usage

1. **Access the Application**
   - Frontend: http://localhost:5173
   - Backend API: http://localhost:3003

2. **Upload Log Files**
   - Navigate to the "Upload Logs" tab
   - Drag and drop or browse for .log or .txt files
   - Maximum file size: 10MB

3. **View Dashboard**
   - Real-time security metrics
   - Threat distribution charts
   - Top source IPs
   - Recent threat activity

4. **Search Logs**
   - Filter by keywords, IP addresses, date ranges
   - Set minimum threat scores
   - Export results to CSV

5. **AI Assistant**
   - Ask questions about your logs
   - Get security recommendations
   - Analyze threat patterns

## 📊 API Endpoints

### Logs
- `POST /api/logs/upload` - Upload log files
- `GET /api/logs/search` - Search logs with filters
- `GET /api/logs` - Get all logs (paginated)
- `GET /api/logs/:id` - Get specific log
- `DELETE /api/logs` - Clear all logs

### Insights
- `GET /api/insights/dashboard` - Dashboard metrics
- `GET /api/insights/threats` - Threat analysis
- `GET /api/insights/network` - Network analysis
- `GET /api/insights/performance` - Performance metrics

### Chat
- `POST /api/chat/query` - Send chat message
- `GET /api/chat/history` - Get conversation history
- `DELETE /api/chat/history` - Clear chat history
- `GET /api/chat/suggestions` - Get suggested queries

### Health
- `GET /api/health` - Basic health check
- `GET /api/health/detailed` - Detailed system status

## 🔒 Security Features

- **Threat Scoring**: Automatic threat level calculation based on log content
- **IP Analysis**: Track and analyze source IP addresses
- **Pattern Detection**: Identify suspicious activity patterns
- **Real-time Monitoring**: Live dashboard updates
- **File Validation**: Secure file upload with type and size restrictions

## 🧪 Development

### Running Tests
```bash
# Frontend tests
cd frontend
npm test

# Backend tests
cd backend
npm test
```

### Building for Production
```bash
# Build frontend
npm run frontend:build

# The built files will be in frontend/dist/
```

### Linting
```bash
npm run lint
```

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add some amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## 📝 License

This project is licensed under the ISC License - see the [LICENSE](LICENSE) file for details.

## 🆘 Support

If you encounter any issues or have questions:

1. Check the [Issues](../../issues) page
2. Create a new issue with detailed information
3. Include logs and error messages when possible

## 🔮 Future Enhancements

- [ ] Database integration (PostgreSQL/MongoDB)
- [ ] User authentication and authorization
- [ ] Real-time log streaming
- [ ] Advanced AI models integration
- [ ] Email/SMS alerting
- [ ] Custom dashboard widgets
- [ ] API rate limiting
- [ ] Docker containerization
- [ ] Kubernetes deployment configs
