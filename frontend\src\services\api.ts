import axios, { AxiosError } from 'axios';
import { 
  UploadResponse, 
  DashboardInsights, 
  LogEntry, 
  SearchFilters, 
  ApiError,
  ThreatAnalysis,
  NetworkAnalysis
} from '../types';

const API_BASE = '/api';

// Create axios instance with default config
const apiClient = axios.create({
  baseURL: API_BASE,
  timeout: 30000, // 30 seconds
  headers: {
    'Content-Type': 'application/json',
  },
});

// Response interceptor for error handling
apiClient.interceptors.response.use(
  (response) => response,
  (error: AxiosError<ApiError>) => {
    console.error('API Error:', error.response?.data || error.message);
    return Promise.reject(error);
  }
);

export const api = {
  // Log endpoints
  uploadLogs: async (file: File): Promise<UploadResponse> => {
    const formData = new FormData();
    formData.append('logFile', file);
    
    const response = await apiClient.post('/logs/upload', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
      timeout: 60000, // 60 seconds for file upload
    });
    
    return response.data;
  },

  searchLogs: async (filters: Partial<SearchFilters>): Promise<{ logs: LogEntry[]; total: number }> => {
    const params = new URLSearchParams();
    Object.entries(filters).forEach(([key, value]) => {
      if (value !== undefined && value !== '') {
        params.append(key, value.toString());
      }
    });
    
    const response = await apiClient.get(`/logs/search?${params}`);
    return response.data;
  },

  getAllLogs: async (page = 1, limit = 50): Promise<{
    logs: LogEntry[];
    total: number;
    page: number;
    limit: number;
    totalPages: number;
  }> => {
    const response = await apiClient.get(`/logs?page=${page}&limit=${limit}`);
    return response.data;
  },

  getLogById: async (id: number): Promise<LogEntry> => {
    const response = await apiClient.get(`/logs/${id}`);
    return response.data;
  },

  getLogStatistics: async (): Promise<DashboardInsights> => {
    const response = await apiClient.get('/logs/stats/overview');
    return response.data;
  },

  deleteLogs: async (): Promise<{ success: boolean; message: string }> => {
    const response = await apiClient.delete('/logs');
    return response.data;
  },

  // Insight endpoints
  getDashboardInsights: async (): Promise<DashboardInsights> => {
    const response = await apiClient.get('/insights/dashboard');
    return response.data;
  },

  getThreatAnalysis: async (): Promise<ThreatAnalysis> => {
    const response = await apiClient.get('/insights/threats');
    return response.data;
  },

  getNetworkAnalysis: async (): Promise<NetworkAnalysis> => {
    const response = await apiClient.get('/insights/network');
    return response.data;
  },

  getPerformanceMetrics: async (): Promise<any> => {
    const response = await apiClient.get('/insights/performance');
    return response.data;
  },

  // Chat endpoints
  sendChatQuery: async (message: string, context?: string): Promise<{ 
    response: string; 
    timestamp: string;
    context?: any;
  }> => {
    const response = await apiClient.post('/chat/query', {
      message,
      context
    });
    return response.data;
  },

  getChatHistory: async (limit = 10): Promise<{
    conversations: Array<{
      id: number;
      message: string;
      response: string;
      timestamp: string;
    }>;
    total: number;
  }> => {
    const response = await apiClient.get(`/chat/history?limit=${limit}`);
    return response.data;
  },

  clearChatHistory: async (): Promise<{ success: boolean; message: string }> => {
    const response = await apiClient.delete('/chat/history');
    return response.data;
  },

  getSuggestedQueries: async (): Promise<{
    suggestions: Array<{
      id: number;
      query: string;
      category: string;
      description: string;
    }>;
    total: number;
  }> => {
    const response = await apiClient.get('/chat/suggestions');
    return response.data;
  },

  // Health endpoints
  getHealth: async (): Promise<{
    status: string;
    timestamp: string;
    uptime: number;
    environment: string;
    version: string;
  }> => {
    const response = await apiClient.get('/health');
    return response.data;
  },

  getDetailedHealth: async (): Promise<any> => {
    const response = await apiClient.get('/health/detailed');
    return response.data;
  }
};

// Error handling utility
export const handleApiError = (error: AxiosError<ApiError>): string => {
  if (error.response?.data?.message) {
    return error.response.data.message;
  }
  if (error.message) {
    return error.message;
  }
  return 'An unexpected error occurred';
};

export default api;
