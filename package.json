{"name": "firewall-log-analyzer", "private": true, "version": "1.0.0", "type": "module", "scripts": {"dev": "concurrently \"npm run server\" \"npm run client\"", "client": "vite", "server": "node server/server.js", "build": "vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@types/node": "^20.10.0", "express": "^4.18.2", "cors": "^2.8.5", "multer": "^1.4.5-lts.1", "mysql2": "^3.6.5", "dotenv": "^16.3.1", "ws": "^8.14.2", "socket.io": "^4.7.4", "axios": "^1.6.2", "date-fns": "^2.30.0", "recharts": "^2.8.0", "react-dropzone": "^14.2.3", "framer-motion": "^10.16.5", "lucide-react": "^0.344.0", "react": "^18.3.1", "react-dom": "^18.3.1", "concurrently": "^8.2.2"}, "devDependencies": {"@eslint/js": "^9.9.1", "@types/react": "^18.3.5", "@types/react-dom": "^18.3.0", "@types/express": "^4.17.21", "@types/cors": "^2.8.17", "@types/multer": "^1.4.11", "@types/ws": "^8.5.10", "@vitejs/plugin-react": "^4.3.1", "autoprefixer": "^10.4.18", "eslint": "^9.9.1", "eslint-plugin-react-hooks": "^5.1.0-rc.0", "eslint-plugin-react-refresh": "^0.4.11", "globals": "^15.9.0", "postcss": "^8.4.35", "tailwindcss": "^3.4.1", "typescript": "^5.5.3", "typescript-eslint": "^8.3.0", "vite": "^5.4.2"}}