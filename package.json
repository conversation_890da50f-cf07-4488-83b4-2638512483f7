{"name": "firewall-log-analyzer", "version": "1.0.0", "description": "AI-powered firewall log analysis platform", "private": true, "workspaces": ["frontend", "backend"], "scripts": {"dev": "concurrently \"npm run backend:dev\" \"npm run frontend:dev\"", "backend:dev": "cd backend && npm run dev", "frontend:dev": "cd frontend && npm run dev", "backend:start": "cd backend && npm start", "frontend:build": "cd frontend && npm run build", "frontend:preview": "cd frontend && npm run preview", "install:all": "npm install && npm install --prefix backend && npm install --prefix frontend", "clean": "rm -rf node_modules backend/node_modules frontend/node_modules", "lint": "npm run lint --prefix frontend", "build": "npm run frontend:build", "preview": "npm run frontend:preview"}, "keywords": ["firewall", "logs", "security", "ai", "analysis", "threat-detection"], "author": "", "license": "ISC", "devDependencies": {"concurrently": "^8.2.2"}}