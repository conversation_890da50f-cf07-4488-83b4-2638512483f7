import React, { useState, useEffect } from 'react';
import { Shield, AlertTriangle, Activity, Eye, TrendingUp } from 'lucide-react';
import { motion } from 'framer-motion';
import { BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, PieChart, Pie, Cell } from 'recharts';
import { api } from '../services/api';
import { DashboardInsights } from '../types';

const Dashboard: React.FC = () => {
  const [insights, setInsights] = useState<DashboardInsights>({
    totalLogs: 0,
    threatsDetected: 0,
    topIPs: [],
    recentActivity: []
  });
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const fetchInsights = async () => {
      try {
        const data = await api.getDashboardInsights();
        setInsights(data);
      } catch (error) {
        console.error('Failed to fetch insights:', error);
      } finally {
        setIsLoading(false);
      }
    };

    fetchInsights();
    const interval = setInterval(fetchInsights, 30000); // Refresh every 30 seconds

    return () => clearInterval(interval);
  }, []);

  const threatLevelData = [
    { name: 'High', value: insights.threatsDetected, color: '#EF4444' },
    { name: 'Medium', value: Math.floor(insights.threatsDetected * 0.6), color: '#F59E0B' },
    { name: 'Low', value: Math.floor(insights.threatsDetected * 0.3), color: '#10B981' },
    { name: 'Safe', value: insights.totalLogs - insights.threatsDetected, color: '#3B82F6' }
  ];

  const StatCard: React.FC<{
    title: string;
    value: string | number;
    icon: React.ReactNode;
    color: string;
    change?: string;
  }> = ({ title, value, icon, color, change }) => (
    <motion.div
      className="bg-gray-800 rounded-xl p-6 border border-gray-700"
      whileHover={{ scale: 1.02 }}
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
    >
      <div className="flex items-center justify-between">
        <div>
          <p className="text-gray-400 text-sm">{title}</p>
          <p className="text-2xl font-bold text-white mt-1">{value}</p>
          {change && (
            <p className="text-sm text-green-400 mt-1 flex items-center gap-1">
              <TrendingUp className="w-3 h-3" />
              {change}
            </p>
          )}
        </div>
        <div className={`w-12 h-12 rounded-lg ${color} flex items-center justify-center`}>
          {icon}
        </div>
      </div>
    </motion.div>
  );

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-cyan-400"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Stats Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <StatCard
          title="Total Logs"
          value={insights.totalLogs.toLocaleString()}
          icon={<Activity className="w-6 h-6 text-white" />}
          color="bg-blue-600"
          change="+12% from last hour"
        />
        <StatCard
          title="Threats Detected"
          value={insights.threatsDetected}
          icon={<AlertTriangle className="w-6 h-6 text-white" />}
          color="bg-red-600"
          change="+5% from last hour"
        />
        <StatCard
          title="Monitoring Status"
          value="Active"
          icon={<Shield className="w-6 h-6 text-white" />}
          color="bg-green-600"
        />
        <StatCard
          title="Unique IPs"
          value={insights.topIPs.length}
          icon={<Eye className="w-6 h-6 text-white" />}
          color="bg-purple-600"
        />
      </div>

      {/* Charts */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <motion.div
          className="bg-gray-800 rounded-xl p-6 border border-gray-700"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
        >
          <h3 className="text-lg font-semibold text-white mb-4">Top Source IPs</h3>
          <ResponsiveContainer width="100%" height={300}>
            <BarChart data={insights.topIPs.slice(0, 8)}>
              <CartesianGrid strokeDasharray="3 3" stroke="#374151" />
              <XAxis dataKey="ip" stroke="#9CA3AF" />
              <YAxis stroke="#9CA3AF" />
              <Tooltip 
                contentStyle={{ 
                  backgroundColor: '#1F2937', 
                  border: '1px solid #374151',
                  borderRadius: '8px'
                }}
              />
              <Bar dataKey="count" fill="#3B82F6" />
            </BarChart>
          </ResponsiveContainer>
        </motion.div>

        <motion.div
          className="bg-gray-800 rounded-xl p-6 border border-gray-700"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
        >
          <h3 className="text-lg font-semibold text-white mb-4">Threat Levels</h3>
          <ResponsiveContainer width="100%" height={300}>
            <PieChart>
              <Pie
                data={threatLevelData}
                cx="50%"
                cy="50%"
                outerRadius={80}
                fill="#8884d8"
                dataKey="value"
                label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
              >
                {threatLevelData.map((entry, index) => (
                  <Cell key={`cell-${index}`} fill={entry.color} />
                ))}
              </Pie>
              <Tooltip 
                contentStyle={{ 
                  backgroundColor: '#1F2937', 
                  border: '1px solid #374151',
                  borderRadius: '8px'
                }}
              />
            </PieChart>
          </ResponsiveContainer>
        </motion.div>
      </div>

      {/* Recent Activity */}
      <motion.div
        className="bg-gray-800 rounded-xl p-6 border border-gray-700"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
      >
        <h3 className="text-lg font-semibold text-white mb-4">Recent Threat Activity</h3>
        <div className="space-y-3">
          {insights.recentActivity.length === 0 ? (
            <p className="text-gray-400 text-center py-8">No recent threats detected</p>
          ) : (
            insights.recentActivity.map((activity, index) => (
              <motion.div
                key={index}
                className="flex items-center gap-4 p-3 bg-gray-700 rounded-lg"
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: index * 0.1 }}
              >
                <div className={`w-3 h-3 rounded-full ${
                  activity.threat_score > 70 ? 'bg-red-500' :
                  activity.threat_score > 30 ? 'bg-yellow-500' : 'bg-green-500'
                }`}></div>
                <div className="flex-1">
                  <p className="text-white text-sm">{activity.message}</p>
                  <div className="flex items-center gap-4 text-xs text-gray-400 mt-1">
                    <span>{new Date(activity.timestamp).toLocaleString()}</span>
                    <span>IP: {activity.source_ip}</span>
                    <span>Threat Score: {activity.threat_score}</span>
                  </div>
                </div>
              </motion.div>
            ))
          )}
        </div>
      </motion.div>
    </div>
  );
};

export default Dashboard;