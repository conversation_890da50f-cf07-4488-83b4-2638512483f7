import ChatModel from '../models/ChatModel.js';

class ChatController {
  async sendQuery(req, res) {
    try {
      const { message, context } = req.body;

      if (!message || typeof message !== 'string' || message.trim().length === 0) {
        return res.status(400).json({ 
          error: 'Message is required and must be a non-empty string' 
        });
      }

      // Validate message length
      if (message.length > 1000) {
        return res.status(400).json({ 
          error: 'Message too long. Maximum 1000 characters allowed.' 
        });
      }

      const response = await ChatModel.generateResponse(message.trim(), context);
      
      res.json(response);
    } catch (error) {
      console.error('Chat query error:', error);
      res.status(500).json({ 
        error: 'Failed to process chat query',
        details: error.message 
      });
    }
  }

  async getConversationHistory(req, res) {
    try {
      const limit = parseInt(req.query.limit) || 10;
      const history = await ChatModel.getConversationHistory(limit);
      
      res.json({
        conversations: history,
        total: history.length
      });
    } catch (error) {
      console.error('Get conversation history error:', error);
      res.status(500).json({ 
        error: 'Failed to retrieve conversation history',
        details: error.message 
      });
    }
  }

  async clearConversations(req, res) {
    try {
      const result = await ChatModel.clearConversations();
      res.json(result);
    } catch (error) {
      console.error('Clear conversations error:', error);
      res.status(500).json({ 
        error: 'Failed to clear conversations',
        details: error.message 
      });
    }
  }

  async getSuggestedQueries(req, res) {
    try {
      const suggestions = [
        {
          id: 1,
          query: "What threats have been detected?",
          category: "threats",
          description: "Get detailed analysis of security threats"
        },
        {
          id: 2,
          query: "Show me a summary of recent activity",
          category: "summary",
          description: "Overview of current log status and statistics"
        },
        {
          id: 3,
          query: "Which IPs are most suspicious?",
          category: "analysis",
          description: "Identify potentially malicious IP addresses"
        },
        {
          id: 4,
          query: "What patterns do you see in the logs?",
          category: "patterns",
          description: "Behavioral analysis and traffic patterns"
        },
        {
          id: 5,
          query: "Give me security recommendations",
          category: "recommendations",
          description: "Actionable security improvement suggestions"
        },
        {
          id: 6,
          query: "Analyze the latest threats",
          category: "threats",
          description: "Focus on most recent security incidents"
        }
      ];

      res.json({
        suggestions,
        total: suggestions.length
      });
    } catch (error) {
      console.error('Get suggested queries error:', error);
      res.status(500).json({ 
        error: 'Failed to retrieve suggested queries',
        details: error.message 
      });
    }
  }
}

export default new ChatController();
