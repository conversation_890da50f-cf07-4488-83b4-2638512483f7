import React, { useCallback, useState } from 'react';
import { useDropzone } from 'react-dropzone';
import { Upload, FileText, AlertCircle, CheckCircle, Info } from 'lucide-react';
import { motion } from 'framer-motion';
import { api, handleApiError } from '../services/api';

interface FileUploadProps {
  onUploadSuccess: () => void;
}

const FileUpload: React.FC<FileUploadProps> = ({ onUploadSuccess }) => {
  const [uploadStatus, setUploadStatus] = useState<'idle' | 'uploading' | 'success' | 'error'>('idle');
  const [uploadMessage, setUploadMessage] = useState('');
  const [uploadProgress, setUploadProgress] = useState(0);

  const onDrop = useCallback(async (acceptedFiles: File[]) => {
    const file = acceptedFiles[0];
    if (!file) return;

    setUploadStatus('uploading');
    setUploadMessage('Processing log file...');
    setUploadProgress(0);

    // Simulate progress
    const progressInterval = setInterval(() => {
      setUploadProgress(prev => {
        if (prev >= 90) {
          clearInterval(progressInterval);
          return 90;
        }
        return prev + 10;
      });
    }, 200);

    try {
      const response = await api.uploadLogs(file);
      clearInterval(progressInterval);
      setUploadProgress(100);
      
      setUploadStatus('success');
      setUploadMessage(
        `${response.message}${response.threatsDetected ? ` (${response.threatsDetected} threats detected)` : ''}`
      );
      onUploadSuccess();
      
      // Reset after 5 seconds
      setTimeout(() => {
        setUploadStatus('idle');
        setUploadMessage('');
        setUploadProgress(0);
      }, 5000);
    } catch (error: any) {
      clearInterval(progressInterval);
      setUploadProgress(0);
      setUploadStatus('error');
      const errorMessage = handleApiError(error);
      setUploadMessage(errorMessage);
      
      setTimeout(() => {
        setUploadStatus('idle');
        setUploadMessage('');
      }, 5000);
    }
  }, [onUploadSuccess]);

  const { getRootProps, getInputProps, isDragActive, fileRejections } = useDropzone({
    onDrop,
    accept: {
      'text/plain': ['.log', '.txt']
    },
    maxFiles: 1,
    maxSize: 10 * 1024 * 1024 // 10MB
  });

  return (
    <div className="space-y-6">
      <div className="bg-gray-800 rounded-xl p-6 border border-gray-700">
        <h2 className="text-xl font-semibold text-white mb-4 flex items-center gap-2">
          <FileText className="w-5 h-5 text-blue-400" />
          Upload Firewall Logs
        </h2>
        
        <motion.div
          {...getRootProps()}
          className={`
            border-2 border-dashed rounded-lg p-8 text-center cursor-pointer transition-all duration-200
            ${isDragActive 
              ? 'border-blue-400 bg-blue-400/10' 
              : 'border-gray-600 hover:border-gray-500'
            }
            ${uploadStatus === 'uploading' ? 'pointer-events-none opacity-50' : ''}
          `}
          whileHover={{ scale: uploadStatus === 'uploading' ? 1 : 1.02 }}
          whileTap={{ scale: uploadStatus === 'uploading' ? 1 : 0.98 }}
        >
          <input {...getInputProps()} />
          
          <div className="flex flex-col items-center gap-4">
            <motion.div
              animate={{ 
                rotate: uploadStatus === 'uploading' ? 360 : 0,
                scale: uploadStatus === 'uploading' ? 1.1 : 1
              }}
              transition={{ 
                rotate: { duration: 1, repeat: uploadStatus === 'uploading' ? Infinity : 0 },
                scale: { duration: 0.2 }
              }}
            >
              {uploadStatus === 'success' ? (
                <CheckCircle className="w-12 h-12 text-green-400" />
              ) : uploadStatus === 'error' ? (
                <AlertCircle className="w-12 h-12 text-red-400" />
              ) : (
                <Upload className="w-12 h-12 text-gray-400" />
              )}
            </motion.div>
            
            <div>
              <p className="text-lg font-medium text-white">
                {uploadStatus === 'uploading' 
                  ? 'Processing...' 
                  : isDragActive 
                    ? 'Drop your log file here' 
                    : 'Drag & drop log files here'
                }
              </p>
              <p className="text-sm text-gray-400 mt-1">
                {uploadStatus === 'idle' && 'Supports .log and .txt files up to 10MB'}
              </p>
            </div>
            
            {uploadStatus !== 'uploading' && (
              <motion.button
                className="px-6 py-2 bg-blue-600 hover:bg-blue-700 disabled:bg-gray-600 text-white rounded-lg transition-colors"
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                disabled={uploadStatus === 'uploading'}
              >
                Browse Files
              </motion.button>
            )}
          </div>
        </motion.div>

        {/* Progress Bar */}
        {uploadStatus === 'uploading' && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            className="mt-4"
          >
            <div className="bg-gray-700 rounded-full h-2">
              <motion.div
                className="bg-blue-600 h-2 rounded-full"
                initial={{ width: 0 }}
                animate={{ width: `${uploadProgress}%` }}
                transition={{ duration: 0.3 }}
              />
            </div>
            <p className="text-sm text-gray-400 mt-2 text-center">
              {uploadProgress}% complete
            </p>
          </motion.div>
        )}
        
        {uploadMessage && (
          <motion.div
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            className={`mt-4 p-3 rounded-lg ${
              uploadStatus === 'success' 
                ? 'bg-green-900/20 border border-green-500/20 text-green-400' 
                : uploadStatus === 'error'
                  ? 'bg-red-900/20 border border-red-500/20 text-red-400'
                  : 'bg-blue-900/20 border border-blue-500/20 text-blue-400'
            }`}
          >
            <p className="text-sm">{uploadMessage}</p>
          </motion.div>
        )}

        {/* File Rejection Errors */}
        {fileRejections.length > 0 && (
          <motion.div
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            className="mt-4 p-3 rounded-lg bg-red-900/20 border border-red-500/20"
          >
            <h4 className="text-red-400 font-medium mb-2">File Upload Errors:</h4>
            {fileRejections.map(({ file, errors }) => (
              <div key={file.name} className="text-sm text-red-300">
                <p className="font-medium">{file.name}</p>
                <ul className="list-disc list-inside ml-2">
                  {errors.map(error => (
                    <li key={error.code}>{error.message}</li>
                  ))}
                </ul>
              </div>
            ))}
          </motion.div>
        )}
      </div>

      {/* Upload Guidelines */}
      <motion.div
        className="bg-blue-900/20 border border-blue-500/20 rounded-xl p-6"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
      >
        <h3 className="text-blue-400 font-semibold mb-3 flex items-center gap-2">
          <Info className="w-5 h-5" />
          Upload Guidelines
        </h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm text-blue-300">
          <div>
            <h4 className="font-medium mb-2">Supported Formats:</h4>
            <ul className="space-y-1">
              <li>• .log files (standard firewall logs)</li>
              <li>• .txt files (plain text logs)</li>
              <li>• Maximum file size: 10MB</li>
            </ul>
          </div>
          <div>
            <h4 className="font-medium mb-2">What happens next:</h4>
            <ul className="space-y-1">
              <li>• Automatic log parsing and analysis</li>
              <li>• Threat detection and scoring</li>
              <li>• Real-time dashboard updates</li>
              <li>• AI-powered insights generation</li>
            </ul>
          </div>
        </div>
      </motion.div>
    </div>
  );
};

export default FileUpload;
