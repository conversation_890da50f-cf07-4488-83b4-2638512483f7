import axios from 'axios';
import { UploadResponse, DashboardInsights, LogEntry, SearchFilters } from '../types';

const API_BASE = '/api';

export const api = {
  uploadLogs: async (file: File): Promise<UploadResponse> => {
    const formData = new FormData();
    formData.append('logFile', file);
    
    const response = await axios.post(`${API_BASE}/logs/upload`, formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
    
    return response.data;
  },

  getDashboardInsights: async (): Promise<DashboardInsights> => {
    const response = await axios.get(`${API_BASE}/insights/dashboard`);
    return response.data;
  },

  searchLogs: async (filters: Partial<SearchFilters>): Promise<{ logs: LogEntry[]; total: number }> => {
    const params = new URLSearchParams();
    Object.entries(filters).forEach(([key, value]) => {
      if (value !== undefined && value !== '') {
        params.append(key, value.toString());
      }
    });
    
    const response = await axios.get(`${API_BASE}/logs/search?${params}`);
    return response.data;
  },

  sendChatQuery: async (message: string, context?: string): Promise<{ response: string; timestamp: string }> => {
    const response = await axios.post(`${API_BASE}/chat/query`, {
      message,
      context
    });
    return response.data;
  }
};