import React, { useState, useEffect } from 'react';
import { Shield, AlertTriangle, Activity, Eye, TrendingUp, RefreshCw } from 'lucide-react';
import { motion } from 'framer-motion';
import { <PERSON><PERSON><PERSON>, Bar, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, PieChart, Pie, Cell } from 'recharts';
import { api, handleApiError } from '../services/api';
import { DashboardInsights } from '../types';

const Dashboard: React.FC = () => {
  const [insights, setInsights] = useState<DashboardInsights>({
    totalLogs: 0,
    threatsDetected: 0,
    topIPs: [],
    recentActivity: []
  });
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [lastUpdated, setLastUpdated] = useState<Date | null>(null);

  const fetchInsights = async () => {
    try {
      setError(null);
      const data = await api.getDashboardInsights();
      setInsights(data);
      setLastUpdated(new Date());
    } catch (error: any) {
      const errorMessage = handleApiError(error);
      setError(errorMessage);
      console.error('Failed to fetch insights:', error);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchInsights();
    const interval = setInterval(fetchInsights, 30000); // Refresh every 30 seconds

    return () => clearInterval(interval);
  }, []);

  const threatLevelData = [
    { name: 'High', value: Math.floor(insights.threatsDetected * 0.2), color: '#EF4444' },
    { name: 'Medium', value: Math.floor(insights.threatsDetected * 0.4), color: '#F59E0B' },
    { name: 'Low', value: Math.floor(insights.threatsDetected * 0.4), color: '#10B981' },
    { name: 'Safe', value: insights.totalLogs - insights.threatsDetected, color: '#3B82F6' }
  ].filter(item => item.value > 0);

  const getThreatLevelColor = (level?: string) => {
    switch (level) {
      case 'high': return 'text-red-400';
      case 'medium': return 'text-yellow-400';
      case 'low': return 'text-green-400';
      default: return 'text-blue-400';
    }
  };

  const StatCard: React.FC<{
    title: string;
    value: string | number;
    icon: React.ReactNode;
    color: string;
    change?: string;
    subtitle?: string;
  }> = ({ title, value, icon, color, change, subtitle }) => (
    <motion.div
      className="bg-gray-800 rounded-xl p-6 border border-gray-700 hover:border-gray-600 transition-colors"
      whileHover={{ scale: 1.02 }}
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
    >
      <div className="flex items-center justify-between">
        <div className="flex-1">
          <p className="text-gray-400 text-sm">{title}</p>
          <p className="text-2xl font-bold text-white mt-1">{value}</p>
          {subtitle && (
            <p className={`text-sm mt-1 ${getThreatLevelColor(subtitle)}`}>
              {subtitle.charAt(0).toUpperCase() + subtitle.slice(1)} Risk
            </p>
          )}
          {change && (
            <p className="text-sm text-green-400 mt-1 flex items-center gap-1">
              <TrendingUp className="w-3 h-3" />
              {change}
            </p>
          )}
        </div>
        <div className={`w-12 h-12 rounded-lg ${color} flex items-center justify-center`}>
          {icon}
        </div>
      </div>
    </motion.div>
  );

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-cyan-400"></div>
        <span className="ml-3 text-gray-400">Loading dashboard...</span>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-red-900/20 border border-red-500/20 rounded-xl p-6">
        <div className="flex items-center gap-3">
          <AlertTriangle className="w-6 h-6 text-red-400" />
          <div>
            <h3 className="text-red-400 font-semibold">Failed to load dashboard</h3>
            <p className="text-red-300 text-sm mt-1">{error}</p>
          </div>
          <button
            onClick={fetchInsights}
            className="ml-auto px-4 py-2 bg-red-600 hover:bg-red-700 text-white rounded-lg transition-colors flex items-center gap-2"
          >
            <RefreshCw className="w-4 h-4" />
            Retry
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header with last updated */}
      <div className="flex items-center justify-between">
        <h2 className="text-2xl font-bold text-white">Security Dashboard</h2>
        <div className="flex items-center gap-3 text-sm text-gray-400">
          {lastUpdated && (
            <span>Last updated: {lastUpdated.toLocaleTimeString()}</span>
          )}
          <button
            onClick={fetchInsights}
            className="p-2 hover:bg-gray-700 rounded-lg transition-colors"
            title="Refresh data"
          >
            <RefreshCw className="w-4 h-4" />
          </button>
        </div>
      </div>

      {/* Stats Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <StatCard
          title="Total Logs"
          value={insights.totalLogs.toLocaleString()}
          icon={<Activity className="w-6 h-6 text-white" />}
          color="bg-blue-600"
          change="+12% from last hour"
        />
        <StatCard
          title="Threats Detected"
          value={insights.threatsDetected}
          icon={<AlertTriangle className="w-6 h-6 text-white" />}
          color="bg-red-600"
          subtitle={insights.threatLevel}
          change="+5% from last hour"
        />
        <StatCard
          title="Risk Score"
          value={insights.riskScore ? `${insights.riskScore}%` : 'N/A'}
          icon={<Shield className="w-6 h-6 text-white" />}
          color="bg-purple-600"
        />
        <StatCard
          title="Unique IPs"
          value={insights.topIPs.length}
          icon={<Eye className="w-6 h-6 text-white" />}
          color="bg-cyan-600"
        />
      </div>

      {/* Charts */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <motion.div
          className="bg-gray-800 rounded-xl p-6 border border-gray-700"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
        >
          <h3 className="text-lg font-semibold text-white mb-4">Top Source IPs</h3>
          {insights.topIPs.length > 0 ? (
            <ResponsiveContainer width="100%" height={300}>
              <BarChart data={insights.topIPs.slice(0, 8)}>
                <CartesianGrid strokeDasharray="3 3" stroke="#374151" />
                <XAxis 
                  dataKey="ip" 
                  stroke="#9CA3AF" 
                  fontSize={12}
                  angle={-45}
                  textAnchor="end"
                  height={80}
                />
                <YAxis stroke="#9CA3AF" />
                <Tooltip 
                  contentStyle={{ 
                    backgroundColor: '#1F2937', 
                    border: '1px solid #374151',
                    borderRadius: '8px',
                    color: '#F9FAFB'
                  }}
                />
                <Bar dataKey="count" fill="#3B82F6" radius={[4, 4, 0, 0]} />
              </BarChart>
            </ResponsiveContainer>
          ) : (
            <div className="flex items-center justify-center h-64 text-gray-400">
              No IP data available
            </div>
          )}
        </motion.div>

        <motion.div
          className="bg-gray-800 rounded-xl p-6 border border-gray-700"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
        >
          <h3 className="text-lg font-semibold text-white mb-4">Threat Distribution</h3>
          {threatLevelData.length > 0 ? (
            <ResponsiveContainer width="100%" height={300}>
              <PieChart>
                <Pie
                  data={threatLevelData}
                  cx="50%"
                  cy="50%"
                  outerRadius={80}
                  fill="#8884d8"
                  dataKey="value"
                  label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                >
                  {threatLevelData.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={entry.color} />
                  ))}
                </Pie>
                <Tooltip 
                  contentStyle={{ 
                    backgroundColor: '#1F2937', 
                    border: '1px solid #374151',
                    borderRadius: '8px',
                    color: '#F9FAFB'
                  }}
                />
              </PieChart>
            </ResponsiveContainer>
          ) : (
            <div className="flex items-center justify-center h-64 text-gray-400">
              No threat data available
            </div>
          )}
        </motion.div>
      </div>

      {/* Recent Activity */}
      <motion.div
        className="bg-gray-800 rounded-xl p-6 border border-gray-700"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
      >
        <h3 className="text-lg font-semibold text-white mb-4">Recent Threat Activity</h3>
        <div className="space-y-3">
          {insights.recentActivity.length === 0 ? (
            <p className="text-gray-400 text-center py-8">No recent threats detected</p>
          ) : (
            insights.recentActivity.map((activity, index) => (
              <motion.div
                key={index}
                className="flex items-center gap-4 p-3 bg-gray-700 rounded-lg hover:bg-gray-600 transition-colors"
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: index * 0.1 }}
              >
                <div className={`w-3 h-3 rounded-full ${
                  activity.threat_score > 70 ? 'bg-red-500' :
                  activity.threat_score > 30 ? 'bg-yellow-500' : 'bg-green-500'
                }`}></div>
                <div className="flex-1">
                  <p className="text-white text-sm">{activity.message}</p>
                  <div className="flex items-center gap-4 text-xs text-gray-400 mt-1">
                    <span>{new Date(activity.timestamp).toLocaleString()}</span>
                    <span>IP: {activity.source_ip}</span>
                    <span className={`px-2 py-1 rounded ${
                      activity.threat_score > 70 ? 'bg-red-900/20 text-red-400' :
                      activity.threat_score > 30 ? 'bg-yellow-900/20 text-yellow-400' : 
                      'bg-green-900/20 text-green-400'
                    }`}>
                      Threat: {activity.threat_score}
                    </span>
                  </div>
                </div>
              </motion.div>
            ))
          )}
        </div>
      </motion.div>

      {/* Recommendations */}
      {insights.recommendations && insights.recommendations.length > 0 && (
        <motion.div
          className="bg-blue-900/20 border border-blue-500/20 rounded-xl p-6"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
        >
          <h3 className="text-lg font-semibold text-blue-400 mb-4">Security Recommendations</h3>
          <ul className="space-y-2">
            {insights.recommendations.map((recommendation, index) => (
              <li key={index} className="text-blue-300 text-sm flex items-start gap-2">
                <span className="text-blue-400 mt-1">•</span>
                {recommendation}
              </li>
            ))}
          </ul>
        </motion.div>
      )}
    </div>
  );
};

export default Dashboard;
