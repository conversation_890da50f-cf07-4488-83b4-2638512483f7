import express from 'express';
import cors from 'cors';
import multer from 'multer';
import path from 'path';
import fs from 'fs';
import { fileURLToPath } from 'url';
import dotenv from 'dotenv';

dotenv.config();

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const app = express();
const PORT = process.env.PORT || 3001;

// Add error handling for server startup
process.on('uncaughtException', (error) => {
  console.error('Uncaught Exception:', error);
  process.exit(1);
});

process.on('unhandledRejection', (reason, promise) => {
  console.error('Unhandled Rejection at:', promise, 'reason:', reason);
  process.exit(1);
});

// Middleware
app.use(cors());
app.use(express.json());
app.use(express.static(path.join(__dirname, '../dist')));

// Ensure uploads directory exists
const uploadsDir = path.join(__dirname, '../uploads');
if (!fs.existsSync(uploadsDir)) {
  fs.mkdirSync(uploadsDir, { recursive: true });
}

// Multer configuration
const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    cb(null, uploadsDir);
  },
  filename: (req, file, cb) => {
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    cb(null, file.fieldname + '-' + uniqueSuffix + path.extname(file.originalname));
  }
});

const upload = multer({ 
  storage: storage,
  fileFilter: (req, file, cb) => {
    const allowedTypes = ['.log', '.txt'];
    const fileExt = path.extname(file.originalname).toLowerCase();
    if (allowedTypes.includes(fileExt)) {
      cb(null, true);
    } else {
      cb(new Error('Only .log and .txt files are allowed'));
    }
  },
  limits: {
    fileSize: 10 * 1024 * 1024 // 10MB limit
  }
});

// Mock data for demonstration
let logs = [];
let insights = {
  totalLogs: 0,
  threatsDetected: 0,
  topIPs: [],
  recentActivity: []
};

// Initialize with some sample data to prevent empty state issues
function initializeSampleData() {
  const sampleLogs = [
    {
      id: 1,
      log_line: "2024-01-15 10:30:15 DENY TCP ************* 80 443 - - - RECEIVE",
      timestamp: new Date().toISOString(),
      source_ip: "*************",
      threat_score: 25,
      metadata_json: JSON.stringify({ sample: true })
    },
    {
      id: 2,
      log_line: "2024-01-15 10:31:22 DENY TCP ********* 22 22 - - - RECEIVE",
      timestamp: new Date().toISOString(),
      source_ip: "*********",
      threat_score: 45,
      metadata_json: JSON.stringify({ sample: true })
    }
    
  ];
  
  logs = sampleLogs;
  updateInsights();
}

// Routes
app.post('/api/logs/upload', upload.single('logFile'), async (req, res) => {
  try {
    if (!req.file) {
      return res.status(400).json({ error: 'No file uploaded' });
    }

    const filePath = req.file.path;
    const fileContent = fs.readFileSync(filePath, 'utf8');
    const lines = fileContent.split('\n').filter(line => line.trim());

    // Process logs (simplified for demo)
    const processedLogs = lines.map((line, index) => {
      const timestamp = new Date();
      const sourceIP = extractIP(line);
      const threatScore = calculateThreatScore(line);
      
      return {
        id: Date.now() + index,
        log_line: line,
        timestamp: timestamp.toISOString(),
        source_ip: sourceIP,
        threat_score: threatScore,
        metadata_json: JSON.stringify({
          file_name: req.file.originalname,
          line_number: index + 1
        })
      };
    });

    logs = [...logs, ...processedLogs];
    updateInsights();

    // Simulate AI processing
    setTimeout(() => {
      generateAIInsights();
    }, 2000);

    res.json({
      success: true,
      message: `Processed ${lines.length} log entries`,
      logsProcessed: lines.length
    });

  } catch (error) {
    console.error('Upload error:', error);
    res.status(500).json({ error: 'Failed to process log file' });
  }
});

app.post('/api/chat/query', async (req, res) => {
  try {
    const { message, context } = req.body;
    
    // Simulate AI response (in production, integrate with Claude API)
    const aiResponse = generateAIResponse(message, context);
    
    res.json({
      response: aiResponse,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    console.error('Chat error:', error);
    res.status(500).json({ error: 'Failed to process chat query' });
  }
});

app.get('/api/insights/dashboard', (req, res) => {
  try {
  res.json(insights);
  } catch (error) {
    console.error('Dashboard insights error:', error);
    res.status(500).json({ error: 'Failed to fetch dashboard insights' });
  }
});

app.get('/api/logs/search', (req, res) => {
  try {
    const { query, startDate, endDate, minThreat } = req.query;
    
    let filteredLogs = logs;
    
    if (query) {
      filteredLogs = filteredLogs.filter(log => 
        log.log_line.toLowerCase().includes(query.toLowerCase()) ||
        log.source_ip?.includes(query)
      );
    }
    
    if (startDate && endDate) {
      filteredLogs = filteredLogs.filter(log => {
        const logDate = new Date(log.timestamp);
        return logDate >= new Date(startDate) && logDate <= new Date(endDate);
      });
    }
    
    if (minThreat) {
      filteredLogs = filteredLogs.filter(log => log.threat_score >= parseInt(minThreat));
    }
    
    res.json({
      logs: filteredLogs.slice(0, 100), // Limit results
      total: filteredLogs.length
    });
  } catch (error) {
    console.error('Log search error:', error);
    res.status(500).json({ error: 'Failed to search logs' });
  }
});

// Health check endpoint
app.get('/api/health', (req, res) => {
  res.json({ status: 'OK', timestamp: new Date().toISOString() });
});

// Helper functions
function extractIP(logLine) {
  const ipRegex = /\b(?:\d{1,3}\.){3}\d{1,3}\b/;
  const match = logLine.match(ipRegex);
  return match ? match[0] : null;
}

function calculateThreatScore(logLine) {
  let score = 0;
  const threats = ['failed', 'denied', 'blocked', 'attack', 'malware', 'suspicious'];
  
  threats.forEach(threat => {
    if (logLine.toLowerCase().includes(threat)) {
      score += 10;
    }
  });
  
  return Math.min(score, 100);
}

function updateInsights() {
  insights.totalLogs = logs.length;
  insights.threatsDetected = logs.filter(log => log.threat_score > 0).length;
  
  // Top IPs
  const ipCounts = {};
  logs.forEach(log => {
    if (log.source_ip) {
      ipCounts[log.source_ip] = (ipCounts[log.source_ip] || 0) + 1;
    }
  });
  
  insights.topIPs = Object.entries(ipCounts)
    .sort(([,a], [,b]) => b - a)
    .slice(0, 10)
    .map(([ip, count]) => ({ ip, count }));
  
  // Recent activity
  insights.recentActivity = logs
    .filter(log => log.threat_score > 0)
    .sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp))
    .slice(0, 5)
    .map(log => ({
      timestamp: log.timestamp,
      message: log.log_line.substring(0, 100) + '...',
      threat_score: log.threat_score,
      source_ip: log.source_ip
    }));
}

function generateAIInsights() {
  // Simulate AI-generated insights
  console.log('Generating AI insights for', logs.length, 'logs');
}

function generateAIResponse(message, context) {
  // Simulate AI response (integrate with Claude API in production)
  const responses = {
    'threats': `🔥 **Threat Analysis**\n\nBased on the logs, I've identified:\n- ${insights.threatsDetected} potential threats\n- Top threat sources: ${insights.topIPs.slice(0, 3).map(ip => ip.ip).join(', ')}\n\n🛡 **Recommendations:**\n- Monitor IP ${insights.topIPs[0]?.ip} for unusual activity\n- Consider implementing rate limiting\n- Review firewall rules for ports 22, 80, 443`,
    'summary': `📊 **Log Summary**\n\n- Total logs processed: ${insights.totalLogs}\n- Threats detected: ${insights.threatsDetected}\n- Most active IPs: ${insights.topIPs.slice(0, 3).map(ip => `${ip.ip} (${ip.count})`).join(', ')}\n\n⏱ **Timeline:** Last 24 hours\n🧠 **Analysis:** High activity from suspicious IPs detected`,
    'default': `I've analyzed your firewall logs and found several patterns. Here's what I discovered:\n\n🔍 **Key Findings:**\n- Multiple failed login attempts\n- Unusual port scanning activity\n- Potential brute force attacks\n\n📈 **Trend Analysis:**\nActivity spikes during off-hours suggest automated attacks. Consider implementing additional security measures.`
  };
  
  const key = Object.keys(responses).find(k => message.toLowerCase().includes(k)) || 'default';
  return responses[key];
}

// Initialize sample data and start server
initializeSampleData();

const server = app.listen(PORT, () => {
  console.log(`🚀 Firewall Log Analyzer running on port ${PORT}`);
  console.log(`📊 Dashboard available at http://localhost:${PORT}`);
  console.log(`🔍 API endpoints ready`);
});

server.on('error', (error) => {
  if (error.code === 'EADDRINUSE') {
    console.error(`❌ Port ${PORT} is already in use. Please try a different port.`);
  } else {
    console.error('❌ Server error:', error);
  }
  process.exit(1);
});