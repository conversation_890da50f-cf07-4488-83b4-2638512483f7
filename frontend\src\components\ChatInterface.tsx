import React, { useState, useRef, useEffect } from 'react';
import { Send, Bot, User, MessageCircle, Loader2, Trash2, Lightbulb } from 'lucide-react';
import { motion, AnimatePresence } from 'framer-motion';
import { useChat } from '../hooks/useChat';

const ChatInterface: React.FC = () => {
  const [input, setInput] = useState('');
  const { messages, isLoading, sendMessage, clearChat, error } = useChat();
  const messagesEndRef = useRef<HTMLDivElement>(null);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  useEffect(scrollToBottom, [messages]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!input.trim() || isLoading) return;

    await sendMessage(input.trim());
    setInput('');
  };

  const handleSuggestedQuery = async (query: string) => {
    setInput(query);
    await sendMessage(query);
    setInput('');
  };

  const suggestedQueries = [
    {
      text: "What threats have been detected?",
      category: "Threats",
      icon: "🔥"
    },
    {
      text: "Show me a summary of recent activity",
      category: "Summary", 
      icon: "📊"
    },
    {
      text: "Which IPs are most suspicious?",
      category: "Analysis",
      icon: "🔍"
    },
    {
      text: "Give me security recommendations",
      category: "Recommendations",
      icon: "💡"
    }
  ];

  return (
    <div className="bg-gray-800 rounded-xl border border-gray-700 h-[700px] flex flex-col">
      <div className="p-4 border-b border-gray-700 flex items-center justify-between">
        <h2 className="text-xl font-semibold text-white flex items-center gap-2">
          <MessageCircle className="w-5 h-5 text-cyan-400" />
          AI Security Assistant
        </h2>
        <div className="flex items-center gap-2">
          <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
          <span className="text-xs text-gray-400 mr-3">AI Online</span>
          <button
            onClick={clearChat}
            className="text-sm text-gray-400 hover:text-white px-3 py-1 rounded-lg hover:bg-gray-700 transition-colors flex items-center gap-1"
            title="Clear conversation"
          >
            <Trash2 className="w-3 h-3" />
            Clear
          </button>
        </div>
      </div>

      <div className="flex-1 overflow-y-auto p-4 space-y-4">
        {messages.length === 0 ? (
          <div className="text-center py-8">
            <motion.div
              initial={{ scale: 0 }}
              animate={{ scale: 1 }}
              transition={{ duration: 0.5 }}
            >
              <Bot className="w-16 h-16 text-cyan-400 mx-auto mb-4" />
            </motion.div>
            <h3 className="text-white text-lg font-semibold mb-2">Welcome to AI Security Assistant</h3>
            <p className="text-gray-400 mb-6">I can help you analyze firewall logs, detect threats, and provide security insights.</p>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-3 max-w-2xl mx-auto">
              {suggestedQueries.map((query, index) => (
                <motion.button
                  key={index}
                  onClick={() => handleSuggestedQuery(query.text)}
                  className="text-left p-4 bg-gray-700 hover:bg-gray-600 rounded-lg transition-colors group"
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: index * 0.1 }}
                >
                  <div className="flex items-start gap-3">
                    <span className="text-lg">{query.icon}</span>
                    <div>
                      <p className="text-white text-sm font-medium group-hover:text-cyan-400 transition-colors">
                        {query.text}
                      </p>
                      <p className="text-xs text-gray-400 mt-1">{query.category}</p>
                    </div>
                  </div>
                </motion.button>
              ))}
            </div>
          </div>
        ) : (
          <AnimatePresence>
            {messages.map((message) => (
              <motion.div
                key={message.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -20 }}
                className={`flex gap-3 ${message.isUser ? 'flex-row-reverse' : ''}`}
              >
                <div className={`w-8 h-8 rounded-full flex items-center justify-center flex-shrink-0 ${
                  message.isUser ? 'bg-blue-600' : 'bg-cyan-600'
                }`}>
                  {message.isUser ? (
                    <User className="w-4 h-4 text-white" />
                  ) : (
                    <Bot className="w-4 h-4 text-white" />
                  )}
                </div>
                <div className={`max-w-[75%] ${message.isUser ? 'text-right' : ''}`}>
                  <div className={`inline-block p-3 rounded-lg ${
                    message.isUser 
                      ? 'bg-blue-600 text-white' 
                      : 'bg-gray-700 text-gray-100'
                  }`}>
                    <div className="whitespace-pre-wrap text-sm leading-relaxed">
                      {message.isUser ? message.message : message.response}
                    </div>
                  </div>
                  <div className={`text-xs text-gray-500 mt-1 ${message.isUser ? 'text-right' : 'text-left'}`}>
                    {new Date(message.timestamp).toLocaleTimeString()}
                  </div>
                </div>
              </motion.div>
            ))}
          </AnimatePresence>
        )}
        
        {isLoading && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="flex gap-3"
          >
            <div className="w-8 h-8 rounded-full bg-cyan-600 flex items-center justify-center">
              <Bot className="w-4 h-4 text-white" />
            </div>
            <div className="bg-gray-700 rounded-lg p-3 flex items-center gap-2">
              <Loader2 className="w-4 h-4 animate-spin text-cyan-400" />
              <span className="text-sm text-gray-300">Analyzing...</span>
            </div>
          </motion.div>
        )}

        {error && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="bg-red-900/20 border border-red-500/20 rounded-lg p-3"
          >
            <p className="text-red-400 text-sm">{error}</p>
          </motion.div>
        )}
        
        <div ref={messagesEndRef} />
      </div>

      {/* Quick Actions */}
      {messages.length > 0 && (
        <div className="px-4 py-2 border-t border-gray-700">
          <div className="flex gap-2 overflow-x-auto">
            {['threats', 'summary', 'recommendations'].map((action) => (
              <button
                key={action}
                onClick={() => handleSuggestedQuery(`Tell me about ${action}`)}
                className="flex-shrink-0 px-3 py-1 text-xs bg-gray-700 hover:bg-gray-600 text-gray-300 rounded-full transition-colors"
                disabled={isLoading}
              >
                {action.charAt(0).toUpperCase() + action.slice(1)}
              </button>
            ))}
          </div>
        </div>
      )}

      <form onSubmit={handleSubmit} className="p-4 border-t border-gray-700">
        <div className="flex gap-2">
          <div className="flex-1 relative">
            <input
              type="text"
              value={input}
              onChange={(e) => setInput(e.target.value)}
              placeholder="Ask about threats, patterns, or get security recommendations..."
              className="w-full bg-gray-700 border border-gray-600 rounded-lg px-4 py-3 pr-12 text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-cyan-500 focus:border-transparent"
              disabled={isLoading}
              maxLength={1000}
            />
            {input.length > 800 && (
              <div className="absolute right-3 top-1/2 transform -translate-y-1/2 text-xs text-gray-400">
                {1000 - input.length}
              </div>
            )}
          </div>
          <motion.button
            type="submit"
            disabled={!input.trim() || isLoading}
            className="px-4 py-3 bg-cyan-600 hover:bg-cyan-700 disabled:bg-gray-600 disabled:cursor-not-allowed text-white rounded-lg transition-colors"
            whileHover={{ scale: !input.trim() || isLoading ? 1 : 1.05 }}
            whileTap={{ scale: !input.trim() || isLoading ? 1 : 0.95 }}
          >
            <Send className="w-4 h-4" />
          </motion.button>
        </div>
        <div className="flex items-center justify-between mt-2 text-xs text-gray-500">
          <span>Press Enter to send</span>
          <span>{input.length}/1000</span>
        </div>
      </form>
    </div>
  );
};

export default ChatInterface;
