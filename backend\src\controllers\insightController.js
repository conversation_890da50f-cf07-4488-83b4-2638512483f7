import LogModel from '../models/LogModel.js';

class InsightController {
  async getDashboardInsights(req, res) {
    try {
      const insights = await LogModel.getLogStats();
      
      // Add additional computed insights
      const enhancedInsights = {
        ...insights,
        threatLevel: this.calculateThreatLevel(insights),
        riskScore: this.calculateRiskScore(insights),
        recommendations: this.generateQuickRecommendations(insights),
        lastUpdated: new Date().toISOString()
      };

      res.json(enhancedInsights);
    } catch (error) {
      console.error('Dashboard insights error:', error);
      res.status(500).json({ 
        error: 'Failed to fetch dashboard insights',
        details: error.message 
      });
    }
  }

  async getThreatAnalysis(req, res) {
    try {
      const logs = await LogModel.getAllLogs();
      
      // Analyze threats by severity
      const threatAnalysis = {
        critical: logs.filter(log => log.threat_score >= 80).length,
        high: logs.filter(log => log.threat_score >= 60 && log.threat_score < 80).length,
        medium: logs.filter(log => log.threat_score >= 30 && log.threat_score < 60).length,
        low: logs.filter(log => log.threat_score > 0 && log.threat_score < 30).length,
        safe: logs.filter(log => log.threat_score === 0).length
      };

      // Threat trends (simulated)
      const threatTrends = this.generateThreatTrends(logs);

      // Top threat types
      const threatTypes = this.analyzeThreatTypes(logs);

      res.json({
        threatAnalysis,
        threatTrends,
        threatTypes,
        totalLogs: logs.length,
        analysisTime: new Date().toISOString()
      });
    } catch (error) {
      console.error('Threat analysis error:', error);
      res.status(500).json({ 
        error: 'Failed to perform threat analysis',
        details: error.message 
      });
    }
  }

  async getNetworkAnalysis(req, res) {
    try {
      const logs = await LogModel.getAllLogs();
      
      // Analyze network patterns
      const networkAnalysis = {
        uniqueIPs: [...new Set(logs.map(log => log.source_ip).filter(Boolean))].length,
        topPorts: this.analyzeTopPorts(logs),
        protocolDistribution: this.analyzeProtocols(logs),
        geographicDistribution: this.analyzeGeography(logs),
        timePatterns: this.analyzeTimePatterns(logs)
      };

      res.json({
        ...networkAnalysis,
        analysisTime: new Date().toISOString()
      });
    } catch (error) {
      console.error('Network analysis error:', error);
      res.status(500).json({ 
        error: 'Failed to perform network analysis',
        details: error.message 
      });
    }
  }

  async getPerformanceMetrics(req, res) {
    try {
      const logs = await LogModel.getAllLogs();
      
      const metrics = {
        totalLogs: logs.length,
        processingRate: this.calculateProcessingRate(logs),
        averageThreatScore: this.calculateAverageThreatScore(logs),
        detectionAccuracy: this.calculateDetectionAccuracy(logs),
        systemHealth: 'optimal',
        uptime: this.calculateUptime(),
        lastProcessed: logs.length > 0 ? logs[logs.length - 1].timestamp : null
      };

      res.json({
        ...metrics,
        timestamp: new Date().toISOString()
      });
    } catch (error) {
      console.error('Performance metrics error:', error);
      res.status(500).json({ 
        error: 'Failed to retrieve performance metrics',
        details: error.message 
      });
    }
  }

  // Helper methods
  calculateThreatLevel(insights) {
    const threatRatio = insights.threatsDetected / insights.totalLogs;
    if (threatRatio > 0.3) return 'high';
    if (threatRatio > 0.1) return 'medium';
    return 'low';
  }

  calculateRiskScore(insights) {
    const threatRatio = insights.threatsDetected / insights.totalLogs;
    return Math.min(Math.round(threatRatio * 100), 100);
  }

  generateQuickRecommendations(insights) {
    const recommendations = [];
    
    if (insights.threatsDetected > 10) {
      recommendations.push('Consider implementing stricter firewall rules');
    }
    
    if (insights.topIPs.length > 0 && insights.topIPs[0].count > 20) {
      recommendations.push(`Monitor IP ${insights.topIPs[0].ip} for potential abuse`);
    }
    
    if (insights.threatsDetected === 0) {
      recommendations.push('System appears secure - maintain current monitoring');
    }

    return recommendations;
  }

  generateThreatTrends(logs) {
    // Simulate trend data (in production, this would analyze historical data)
    return {
      hourly: Array.from({ length: 24 }, (_, i) => ({
        hour: i,
        threats: Math.floor(Math.random() * 10)
      })),
      daily: Array.from({ length: 7 }, (_, i) => ({
        day: i,
        threats: Math.floor(Math.random() * 50)
      }))
    };
  }

  analyzeThreatTypes(logs) {
    const types = {
      'Port Scanning': logs.filter(log => log.log_line.includes('scan')).length,
      'Brute Force': logs.filter(log => log.log_line.includes('failed')).length,
      'DDoS': logs.filter(log => log.threat_score > 70).length,
      'Malware': logs.filter(log => log.log_line.includes('malware')).length
    };

    return Object.entries(types)
      .map(([type, count]) => ({ type, count }))
      .sort((a, b) => b.count - a.count);
  }

  analyzeTopPorts(logs) {
    const ports = {};
    logs.forEach(log => {
      const portMatch = log.log_line.match(/\s(\d{1,5})\s/g);
      if (portMatch) {
        portMatch.forEach(port => {
          const p = port.trim();
          ports[p] = (ports[p] || 0) + 1;
        });
      }
    });

    return Object.entries(ports)
      .sort(([,a], [,b]) => b - a)
      .slice(0, 10)
      .map(([port, count]) => ({ port, count }));
  }

  analyzeProtocols(logs) {
    const protocols = { TCP: 0, UDP: 0, ICMP: 0 };
    logs.forEach(log => {
      if (log.log_line.includes('TCP')) protocols.TCP++;
      else if (log.log_line.includes('UDP')) protocols.UDP++;
      else if (log.log_line.includes('ICMP')) protocols.ICMP++;
    });

    return protocols;
  }

  analyzeGeography(logs) {
    // Simulate geographic data (in production, use IP geolocation)
    return {
      'United States': 45,
      'China': 23,
      'Russia': 15,
      'Germany': 8,
      'Other': 9
    };
  }

  analyzeTimePatterns(logs) {
    const hours = Array(24).fill(0);
    logs.forEach(log => {
      const hour = new Date(log.timestamp).getHours();
      hours[hour]++;
    });

    return hours.map((count, hour) => ({ hour, count }));
  }

  calculateProcessingRate(logs) {
    // Simulate processing rate (logs per minute)
    return logs.length > 0 ? Math.round(logs.length / 60) : 0;
  }

  calculateAverageThreatScore(logs) {
    if (logs.length === 0) return 0;
    const total = logs.reduce((sum, log) => sum + log.threat_score, 0);
    return Math.round(total / logs.length);
  }

  calculateDetectionAccuracy(logs) {
    // Simulate detection accuracy percentage
    return 94.5;
  }

  calculateUptime() {
    // Simulate system uptime in hours
    return 168; // 1 week
  }
}

export default new InsightController();
