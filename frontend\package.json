{"name": "firewall-log-analyzer-frontend", "private": true, "version": "1.0.0", "description": "Frontend for Firewall Log Analyzer", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"react": "^18.3.1", "react-dom": "^18.3.1", "axios": "^1.6.2", "date-fns": "^2.30.0", "recharts": "^2.8.0", "react-dropzone": "^14.2.3", "framer-motion": "^10.16.5", "lucide-react": "^0.344.0", "clsx": "^2.0.0"}, "devDependencies": {"@eslint/js": "^9.9.1", "@types/react": "^18.3.5", "@types/react-dom": "^18.3.0", "@vitejs/plugin-react": "^4.3.1", "autoprefixer": "^10.4.18", "eslint": "^9.9.1", "eslint-plugin-react-hooks": "^5.1.0-rc.0", "eslint-plugin-react-refresh": "^0.4.11", "globals": "^15.9.0", "postcss": "^8.4.35", "tailwindcss": "^3.4.1", "typescript": "^5.5.3", "typescript-eslint": "^8.3.0", "vite": "^5.4.2"}}