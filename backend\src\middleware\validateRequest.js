class ValidationError extends Error {
  constructor(message, details = []) {
    super(message);
    this.name = 'ValidationError';
    this.details = details;
  }
}

const validateRequest = {
  // Validate chat message
  chatMessage: (req, res, next) => {
    const { message, context } = req.body;
    const errors = [];

    if (!message) {
      errors.push('Message is required');
    } else if (typeof message !== 'string') {
      errors.push('Message must be a string');
    } else if (message.trim().length === 0) {
      errors.push('Message cannot be empty');
    } else if (message.length > 1000) {
      errors.push('Message cannot exceed 1000 characters');
    }

    if (context && typeof context !== 'string') {
      errors.push('Context must be a string');
    }

    if (errors.length > 0) {
      return next(new ValidationError('Invalid chat message', errors));
    }

    next();
  },

  // Validate search logs parameters
  searchLogs: (req, res, next) => {
    const { query, startDate, endDate, minThreat } = req.query;
    const errors = [];

    if (query && typeof query !== 'string') {
      errors.push('Query must be a string');
    }

    if (startDate && !isValidDate(startDate)) {
      errors.push('Start date must be a valid ISO date string');
    }

    if (endDate && !isValidDate(endDate)) {
      errors.push('End date must be a valid ISO date string');
    }

    if (startDate && endDate && new Date(startDate) > new Date(endDate)) {
      errors.push('Start date cannot be after end date');
    }

    if (minThreat && (!isValidNumber(minThreat) || parseInt(minThreat) < 0 || parseInt(minThreat) > 100)) {
      errors.push('Minimum threat score must be a number between 0 and 100');
    }

    if (errors.length > 0) {
      return next(new ValidationError('Invalid search parameters', errors));
    }

    next();
  },

  // Validate pagination parameters
  pagination: (req, res, next) => {
    const { page, limit } = req.query;
    const errors = [];

    if (page && (!isValidNumber(page) || parseInt(page) < 1)) {
      errors.push('Page must be a positive integer');
    }

    if (limit && (!isValidNumber(limit) || parseInt(limit) < 1 || parseInt(limit) > 1000)) {
      errors.push('Limit must be a positive integer between 1 and 1000');
    }

    if (errors.length > 0) {
      return next(new ValidationError('Invalid pagination parameters', errors));
    }

    next();
  },

  // Validate log ID parameter
  logId: (req, res, next) => {
    const { id } = req.params;
    const errors = [];

    if (!id) {
      errors.push('Log ID is required');
    } else if (!isValidNumber(id) || parseInt(id) < 1) {
      errors.push('Log ID must be a positive integer');
    }

    if (errors.length > 0) {
      return next(new ValidationError('Invalid log ID', errors));
    }

    next();
  }
};

// Helper functions
function isValidDate(dateString) {
  const date = new Date(dateString);
  return date instanceof Date && !isNaN(date.getTime());
}

function isValidNumber(value) {
  return !isNaN(value) && !isNaN(parseInt(value));
}

export default validateRequest;
