{"name": "firewall-log-analyzer-backend", "version": "1.0.0", "description": "Backend API for Firewall Log Analyzer", "type": "module", "main": "src/server.js", "scripts": {"start": "node src/server.js", "dev": "node src/server.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["firewall", "logs", "security", "api"], "author": "", "license": "ISC", "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "multer": "^1.4.5-lts.1", "mysql2": "^3.6.5", "dotenv": "^16.3.1", "ws": "^8.14.2", "socket.io": "^4.7.4", "axios": "^1.6.2", "date-fns": "^2.30.0"}, "devDependencies": {"@types/express": "^4.17.21", "@types/cors": "^2.8.17", "@types/multer": "^1.4.11", "@types/ws": "^8.5.10", "@types/node": "^20.10.0"}}