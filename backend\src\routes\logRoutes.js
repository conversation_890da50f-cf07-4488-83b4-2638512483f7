import express from 'express';
import logController from '../controllers/logController.js';
import uploadMiddleware from '../middleware/uploadMiddleware.js';
import validateRequest from '../middleware/validateRequest.js';

const router = express.Router();

// Upload logs endpoint
router.post('/upload', 
  uploadMiddleware.single('logFile'),
  logController.uploadLogs
);

// Search logs endpoint
router.get('/search', 
  validateRequest.searchLogs,
  logController.searchLogs
);

// Get all logs with pagination
router.get('/', 
  validateRequest.pagination,
  logController.getAllLogs
);

// Get specific log by ID
router.get('/:id', 
  validateRequest.logId,
  logController.getLogById
);

// Get log statistics
router.get('/stats/overview', 
  logController.getLogStatistics
);

// Delete all logs (admin only)
router.delete('/', 
  logController.deleteLogs
);

export default router;
