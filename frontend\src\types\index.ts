export interface LogEntry {
  id: number;
  log_line: string;
  timestamp: string;
  source_ip: string | null;
  threat_score: number;
  metadata_json: string;
}

export interface ChatMessage {
  id: string;
  message: string;
  response: string;
  timestamp: string;
  isUser: boolean;
}

export interface DashboardInsights {
  totalLogs: number;
  threatsDetected: number;
  topIPs: Array<{ ip: string; count: number }>;
  recentActivity: Array<{
    timestamp: string;
    message: string;
    threat_score: number;
    source_ip: string;
  }>;
  threatLevel?: 'low' | 'medium' | 'high';
  riskScore?: number;
  recommendations?: string[];
  lastUpdated?: string;
}

export interface UploadResponse {
  success: boolean;
  message: string;
  logsProcessed: number;
  threatsDetected?: number;
}

export interface SearchFilters {
  query: string;
  startDate: string;
  endDate: string;
  minThreat: number;
}

export interface ApiError {
  error: string;
  message: string;
  details?: string;
}

export interface ThreatAnalysis {
  threatAnalysis: {
    critical: number;
    high: number;
    medium: number;
    low: number;
    safe: number;
  };
  threatTrends: {
    hourly: Array<{ hour: number; threats: number }>;
    daily: Array<{ day: number; threats: number }>;
  };
  threatTypes: Array<{ type: string; count: number }>;
  totalLogs: number;
  analysisTime: string;
}

export interface NetworkAnalysis {
  uniqueIPs: number;
  topPorts: Array<{ port: string; count: number }>;
  protocolDistribution: { TCP: number; UDP: number; ICMP: number };
  geographicDistribution: Record<string, number>;
  timePatterns: Array<{ hour: number; count: number }>;
  analysisTime: string;
}
