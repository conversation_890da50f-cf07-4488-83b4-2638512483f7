import React, { useCallback, useState } from 'react';
import { useDropzone } from 'react-dropzone';
import { Upload, FileText, AlertCircle, CheckCircle } from 'lucide-react';
import { motion } from 'framer-motion';
import { api } from '../services/api';

interface FileUploadProps {
  onUploadSuccess: () => void;
}

const FileUpload: React.FC<FileUploadProps> = ({ onUploadSuccess }) => {
  const [uploadStatus, setUploadStatus] = useState<'idle' | 'uploading' | 'success' | 'error'>('idle');
  const [uploadMessage, setUploadMessage] = useState('');

  const onDrop = useCallback(async (acceptedFiles: File[]) => {
    const file = acceptedFiles[0];
    if (!file) return;

    setUploadStatus('uploading');
    setUploadMessage('Processing log file...');

    try {
      const response = await api.uploadLogs(file);
      setUploadStatus('success');
      setUploadMessage(response.message);
      onUploadSuccess();
      
      // Reset after 3 seconds
      setTimeout(() => {
        setUploadStatus('idle');
        setUploadMessage('');
      }, 3000);
    } catch (error) {
      setUploadStatus('error');
      setUploadMessage('Failed to upload file. Please try again.');
      
      setTimeout(() => {
        setUploadStatus('idle');
        setUploadMessage('');
      }, 3000);
    }
  }, [onUploadSuccess]);

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: {
      'text/plain': ['.log', '.txt']
    },
    maxFiles: 1,
    maxSize: 10 * 1024 * 1024 // 10MB
  });

  return (
    <div className="bg-gray-800 rounded-xl p-6 border border-gray-700">
      <h2 className="text-xl font-semibold text-white mb-4 flex items-center gap-2">
        <FileText className="w-5 h-5 text-blue-400" />
        Upload Firewall Logs
      </h2>
      
      <motion.div
        {...getRootProps()}
        className={`
          border-2 border-dashed rounded-lg p-8 text-center cursor-pointer transition-all duration-200
          ${isDragActive 
            ? 'border-blue-400 bg-blue-400/10' 
            : 'border-gray-600 hover:border-gray-500'
          }
          ${uploadStatus === 'uploading' ? 'pointer-events-none opacity-50' : ''}
        `}
        whileHover={{ scale: 1.02 }}
        whileTap={{ scale: 0.98 }}
      >
        <input {...getInputProps()} />
        
        <div className="flex flex-col items-center gap-4">
          <motion.div
            animate={{ 
              rotate: uploadStatus === 'uploading' ? 360 : 0,
              scale: uploadStatus === 'uploading' ? 1.1 : 1
            }}
            transition={{ 
              rotate: { duration: 1, repeat: uploadStatus === 'uploading' ? Infinity : 0 },
              scale: { duration: 0.2 }
            }}
          >
            {uploadStatus === 'success' ? (
              <CheckCircle className="w-12 h-12 text-green-400" />
            ) : uploadStatus === 'error' ? (
              <AlertCircle className="w-12 h-12 text-red-400" />
            ) : (
              <Upload className="w-12 h-12 text-gray-400" />
            )}
          </motion.div>
          
          <div>
            <p className="text-lg font-medium text-white">
              {uploadStatus === 'uploading' 
                ? 'Processing...' 
                : isDragActive 
                  ? 'Drop your log file here' 
                  : 'Drag & drop log files here'
              }
            </p>
            <p className="text-sm text-gray-400 mt-1">
              {uploadStatus === 'idle' && 'Supports .log and .txt files up to 10MB'}
            </p>
          </div>
          
          {uploadStatus !== 'uploading' && (
            <motion.button
              className="px-6 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors"
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              Browse Files
            </motion.button>
          )}
        </div>
      </motion.div>
      
      {uploadMessage && (
        <motion.div
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          className={`mt-4 p-3 rounded-lg ${
            uploadStatus === 'success' 
              ? 'bg-green-900/20 border border-green-500/20 text-green-400' 
              : uploadStatus === 'error'
                ? 'bg-red-900/20 border border-red-500/20 text-red-400'
                : 'bg-blue-900/20 border border-blue-500/20 text-blue-400'
          }`}
        >
          <p className="text-sm">{uploadMessage}</p>
        </motion.div>
      )}
    </div>
  );
};

export default FileUpload;