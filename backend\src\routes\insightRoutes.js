import express from 'express';
import insightController from '../controllers/insightController.js';

const router = express.Router();

// Get dashboard insights
router.get('/dashboard', 
  insightController.getDashboardInsights
);

// Get threat analysis
router.get('/threats', 
  insightController.getThreatAnalysis
);

// Get network analysis
router.get('/network', 
  insightController.getNetworkAnalysis
);

// Get performance metrics
router.get('/performance', 
  insightController.getPerformanceMetrics
);

export default router;
